<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الكامل - الكمية المسحوبة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .clear-button {
            background: #f44336;
        }
        .clear-button:hover {
            background: #da190b;
        }
        .stat-card {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }
        .success {
            color: green;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: red;
            background: #ffeaea;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>اختبار النظام الكامل - إحصائيات الكمية المسحوبة</h2>
        
        <div class="test-section">
            <h3>الإحصائيات الحالية</h3>
            <div class="stat-card">
                <span>إجمالي الكمية المسحوبة:</span>
                <span class="stat-value" id="totalWithdrawn">0</span>
            </div>
            <div class="stat-card">
                <span>عدد سجلات السحب:</span>
                <span class="stat-value" id="withdrawalCount">0</span>
            </div>
            <div class="stat-card">
                <span>إجمالي القيمة المسحوبة:</span>
                <span class="stat-value" id="totalValue">0 ج.م</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>إجراءات الاختبار</h3>
            <button class="test-button" onclick="loadSampleData()">تحميل بيانات تجريبية</button>
            <button class="test-button" onclick="addMoreData()">إضافة المزيد من البيانات</button>
            <button class="test-button clear-button" onclick="clearAllData()">مسح جميع البيانات</button>
            <button class="test-button" onclick="updateStats()">تحديث الإحصائيات</button>
        </div>
        
        <div class="test-section">
            <h3>اختبار الحسابات</h3>
            <button class="test-button" onclick="testCalculations()">اختبار دقة الحسابات</button>
            <div id="calculationResults"></div>
        </div>
        
        <div class="test-section">
            <h3>عرض البيانات</h3>
            <button class="test-button" onclick="showWithdrawalData()">عرض سجلات السحب</button>
            <div id="dataDisplay"></div>
        </div>
        
        <div id="testResults"></div>
    </div>

    <script>
        // تحميل بيانات تجريبية
        function loadSampleData() {
            const sampleWithdrawals = [
                {
                    id: '1',
                    workerId: 'w1',
                    workerName: 'أحمد محمد',
                    workerJob: 'خياط',
                    productId: 'p1',
                    productName: 'شنطة جلد بني',
                    productCode: 'BAG001',
                    quantity: 12,
                    unitPrice: 45,
                    totalValue: 540,
                    notes: 'سحب للإنتاج',
                    date: '2024-01-15',
                    time: '09:30:00',
                    timestamp: '2024-01-15T09:30:00.000Z'
                },
                {
                    id: '2',
                    workerId: 'w2',
                    workerName: 'محمد علي',
                    workerJob: 'مساعد',
                    productId: 'p2',
                    productName: 'شنطة قماش أزرق',
                    productCode: 'BAG002',
                    quantity: 8,
                    unitPrice: 30,
                    totalValue: 240,
                    notes: 'سحب للتجميع',
                    date: '2024-01-16',
                    time: '10:15:00',
                    timestamp: '2024-01-16T10:15:00.000Z'
                },
                {
                    id: '3',
                    workerId: 'w3',
                    workerName: 'علي حسن',
                    workerJob: 'فني',
                    productId: 'p3',
                    productName: 'شنطة رياضية',
                    productCode: 'BAG003',
                    quantity: 15,
                    unitPrice: 60,
                    totalValue: 900,
                    notes: 'سحب للتشطيب',
                    date: '2024-01-17',
                    time: '11:00:00',
                    timestamp: '2024-01-17T11:00:00.000Z'
                }
            ];
            
            localStorage.setItem('bagFactory_workerWithdrawals', JSON.stringify(sampleWithdrawals));
            updateStats();
            showMessage('تم تحميل البيانات التجريبية بنجاح!', 'success');
        }
        
        // إضافة المزيد من البيانات
        function addMoreData() {
            const existing = JSON.parse(localStorage.getItem('bagFactory_workerWithdrawals') || '[]');
            const newData = [
                {
                    id: '4',
                    workerId: 'w4',
                    workerName: 'سامي أحمد',
                    workerJob: 'مشرف',
                    productId: 'p4',
                    productName: 'شنطة مدرسية',
                    productCode: 'BAG004',
                    quantity: 20,
                    unitPrice: 25,
                    totalValue: 500,
                    notes: 'سحب للتوزيع',
                    date: '2024-01-18',
                    time: '08:45:00',
                    timestamp: '2024-01-18T08:45:00.000Z'
                }
            ];
            
            const combined = [...existing, ...newData];
            localStorage.setItem('bagFactory_workerWithdrawals', JSON.stringify(combined));
            updateStats();
            showMessage('تم إضافة بيانات جديدة!', 'success');
        }
        
        // مسح جميع البيانات
        function clearAllData() {
            localStorage.removeItem('bagFactory_workerWithdrawals');
            updateStats();
            showMessage('تم مسح جميع البيانات!', 'error');
        }
        
        // تحديث الإحصائيات
        function updateStats() {
            const saved = localStorage.getItem('bagFactory_workerWithdrawals');
            const withdrawals = saved ? JSON.parse(saved) : [];
            
            const totalQuantity = withdrawals.reduce((sum, w) => sum + w.quantity, 0);
            const totalValue = withdrawals.reduce((sum, w) => sum + w.totalValue, 0);
            const count = withdrawals.length;
            
            document.getElementById('totalWithdrawn').textContent = totalQuantity.toLocaleString();
            document.getElementById('withdrawalCount').textContent = count;
            document.getElementById('totalValue').textContent = totalValue.toLocaleString() + ' ج.م';
        }
        
        // اختبار دقة الحسابات
        function testCalculations() {
            const saved = localStorage.getItem('bagFactory_workerWithdrawals');
            const withdrawals = saved ? JSON.parse(saved) : [];
            
            let results = '<h4>نتائج اختبار الحسابات:</h4>';
            
            if (withdrawals.length === 0) {
                results += '<p class="error">لا توجد بيانات للاختبار</p>';
            } else {
                const totalQuantity = withdrawals.reduce((sum, w) => sum + w.quantity, 0);
                const totalValue = withdrawals.reduce((sum, w) => sum + w.totalValue, 0);
                const manualTotal = withdrawals.reduce((sum, w) => sum + (w.quantity * w.unitPrice), 0);
                
                results += `<p><strong>إجمالي الكمية:</strong> ${totalQuantity}</p>`;
                results += `<p><strong>إجمالي القيمة المحفوظة:</strong> ${totalValue.toLocaleString()} ج.م</p>`;
                results += `<p><strong>إجمالي القيمة المحسوبة يدوياً:</strong> ${manualTotal.toLocaleString()} ج.م</p>`;
                
                if (totalValue === manualTotal) {
                    results += '<p class="success">✓ الحسابات صحيحة!</p>';
                } else {
                    results += '<p class="error">✗ خطأ في الحسابات!</p>';
                }
            }
            
            document.getElementById('calculationResults').innerHTML = results;
        }
        
        // عرض سجلات السحب
        function showWithdrawalData() {
            const saved = localStorage.getItem('bagFactory_workerWithdrawals');
            const withdrawals = saved ? JSON.parse(saved) : [];
            
            let display = '<h4>سجلات السحب:</h4>';
            
            if (withdrawals.length === 0) {
                display += '<p>لا توجد سجلات</p>';
            } else {
                display += '<table border="1" style="width: 100%; border-collapse: collapse;">';
                display += '<tr><th>العامل</th><th>المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th><th>التاريخ</th></tr>';
                
                withdrawals.forEach(w => {
                    display += `<tr>
                        <td>${w.workerName}</td>
                        <td>${w.productName}</td>
                        <td>${w.quantity}</td>
                        <td>${w.unitPrice} ج.م</td>
                        <td>${w.totalValue} ج.م</td>
                        <td>${w.date}</td>
                    </tr>`;
                });
                
                display += '</table>';
            }
            
            document.getElementById('dataDisplay').innerHTML = display;
        }
        
        // عرض رسالة
        function showMessage(message, type) {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = message;
            document.getElementById('testResults').appendChild(div);
            
            setTimeout(() => {
                div.remove();
            }, 3000);
        }
        
        // تحديث الإحصائيات عند تحميل الصفحة
        updateStats();
    </script>
</body>
</html>
