<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد التراخيص - للمطور فقط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .generator-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .generator-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn {
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .license-output {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .developer-info {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .warning-box {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="generator-container">
        <!-- معلومات المطور -->
        <div class="developer-info">
            <h2><i class="bi bi-code-slash me-2"></i>مولد التراخيص</h2>
            <p class="mb-0">أداة المطور لتوليد تراخيص العملاء</p>
            <small>البشمهندس أحمد يونس - 01100693019</small>
        </div>

        <!-- تحذير أمني -->
        <div class="warning-box">
            <h5><i class="bi bi-shield-exclamation me-2"></i>تحذير أمني</h5>
            <p class="mb-0">هذه الأداة مخصصة للمطور فقط. لا تشارك هذا الملف مع العملاء أو أي شخص آخر.</p>
        </div>

        <div class="generator-card">
            <h3 class="text-center mb-4">
                <i class="bi bi-key-fill me-2"></i>
                توليد ترخيص جديد
            </h3>

            <form id="licenseForm">
                <!-- معلومات العميل -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <label for="customerName" class="form-label">اسم العميل *</label>
                        <input type="text" class="form-control" id="customerName" required>
                    </div>
                    <div class="col-md-6">
                        <label for="customerPhone" class="form-label">رقم الهاتف *</label>
                        <input type="tel" class="form-control" id="customerPhone" required>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <label for="customerEmail" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="customerEmail">
                    </div>
                    <div class="col-md-6">
                        <label for="customerCompany" class="form-label">اسم الشركة</label>
                        <input type="text" class="form-control" id="customerCompany">
                    </div>
                </div>

                <!-- نوع الترخيص -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <label for="licenseType" class="form-label">نوع الترخيص *</label>
                        <select class="form-select" id="licenseType" required>
                            <option value="">اختر نوع الترخيص</option>
                            <option value="full">ترخيص كامل</option>
                            <option value="trial">ترخيص تجريبي</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="duration" class="form-label">مدة الترخيص *</label>
                        <select class="form-select" id="duration" required>
                            <option value="">اختر المدة</option>
                            <option value="1">شهر واحد</option>
                            <option value="3">3 أشهر</option>
                            <option value="6">6 أشهر</option>
                            <option value="12">سنة واحدة</option>
                            <option value="24">سنتان</option>
                            <option value="36">3 سنوات</option>
                            <option value="custom">مدة مخصصة</option>
                        </select>
                    </div>
                </div>

                <!-- تاريخ مخصص -->
                <div class="row mb-4" id="customDateRow" style="display: none;">
                    <div class="col-md-6">
                        <label for="customStartDate" class="form-label">تاريخ البداية</label>
                        <input type="date" class="form-control" id="customStartDate">
                    </div>
                    <div class="col-md-6">
                        <label for="customEndDate" class="form-label">تاريخ الانتهاء</label>
                        <input type="date" class="form-control" id="customEndDate">
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="mb-4">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" rows="3" placeholder="ملاحظات إضافية عن الترخيص..."></textarea>
                </div>

                <!-- أزرار التحكم -->
                <div class="text-center mb-4">
                    <button type="button" class="btn btn-primary btn-lg me-2" onclick="generateLicense()">
                        <i class="bi bi-gear-fill me-2"></i>
                        توليد الترخيص
                    </button>
                    <button type="button" class="btn btn-secondary btn-lg" onclick="clearForm()">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        مسح البيانات
                    </button>
                </div>
            </form>

            <!-- عرض الترخيص المولد -->
            <div id="licenseOutput" style="display: none;">
                <h5><i class="bi bi-key me-2"></i>مفتاح الترخيص المولد</h5>
                <div class="license-output" id="generatedLicense"></div>
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-success me-2" onclick="copyLicense()">
                        <i class="bi bi-clipboard me-2"></i>
                        نسخ الترخيص
                    </button>
                    <button type="button" class="btn btn-info me-2" onclick="saveLicenseFile()">
                        <i class="bi bi-download me-2"></i>
                        حفظ كملف
                    </button>
                    <button type="button" class="btn btn-warning" onclick="testLicense()">
                        <i class="bi bi-bug me-2"></i>
                        اختبار الترخيص
                    </button>
                </div>
            </div>

            <!-- معلومات الترخيص -->
            <div id="licenseInfo" style="display: none;" class="mt-4">
                <h6><i class="bi bi-info-circle me-2"></i>معلومات الترخيص</h6>
                <div class="row">
                    <div class="col-md-6">
                        <small><strong>معرف الترخيص:</strong> <span id="infoLicenseId" class="text-primary"></span></small><br>
                        <small><strong>العميل:</strong> <span id="infoCustomerName"></span></small><br>
                        <small><strong>الهاتف:</strong> <span id="infoCustomerPhone"></span></small><br>
                        <small><strong>النوع:</strong> <span id="infoLicenseType"></span></small>
                    </div>
                    <div class="col-md-6">
                        <small><strong>تاريخ البداية:</strong> <span id="infoStartDate"></span></small><br>
                        <small><strong>تاريخ الانتهاء:</strong> <span id="infoEndDate"></span></small><br>
                        <small><strong>المدة:</strong> <span id="infoDuration"></span></small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentLicenseData = null;

        // تشفير البيانات (نفس الطريقة المستخدمة في النظام الأساسي)
        function encrypt(text, key) {
            let result = '';
            for (let i = 0; i < text.length; i++) {
                result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
            }
            return btoa(result);
        }

        // فك التشفير
        function decrypt(encryptedText, key) {
            try {
                const text = atob(encryptedText);
                let result = '';
                for (let i = 0; i < text.length; i++) {
                    result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
                }
                return result;
            } catch (e) {
                return null;
            }
        }

        // توليد مفتاح تشفير فريد للجهاز (نفس الطريقة المستخدمة في النظام الأساسي)
        function generateDeviceKey() {
            const userAgent = navigator.userAgent;
            const language = navigator.language;
            const platform = navigator.platform;
            const screenRes = screen.width + 'x' + screen.height;
            return btoa(userAgent + language + platform + screenRes).substring(0, 32);
        }

        // توليد مفتاح تشفير افتراضي للاختبار
        function generateDefaultDeviceKey() {
            // للاختبار فقط - استخدم مفتاح الجهاز الحقيقي
            return generateDeviceKey();
        }

        // تهيئة الصفحة عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // معالجة تغيير نوع المدة
            document.getElementById('duration').addEventListener('change', function() {
                const customDateRow = document.getElementById('customDateRow');
                if (this.value === 'custom') {
                    customDateRow.style.display = 'block';
                } else {
                    customDateRow.style.display = 'none';
                }
            });
        });

        // توليد الترخيص
        function generateLicense() {
            try {
                // التحقق من البيانات المطلوبة
                const customerName = document.getElementById('customerName').value.trim();
                const customerPhone = document.getElementById('customerPhone').value.trim();
                const licenseType = document.getElementById('licenseType').value;
                const duration = document.getElementById('duration').value;

                if (!customerName || !customerPhone || !licenseType || !duration) {
                    alert('يرجى ملء جميع البيانات المطلوبة');
                    return;
                }

                // التحقق من صحة رقم الهاتف
                if (!/^[0-9+\-\s()]+$/.test(customerPhone)) {
                    alert('يرجى إدخال رقم هاتف صحيح');
                    return;
                }

            // حساب تاريخ الانتهاء
            let startDate = new Date();
            let endDate = new Date();

            if (duration === 'custom') {
                const customStartDate = document.getElementById('customStartDate').value;
                const customEndDate = document.getElementById('customEndDate').value;
                
                if (!customStartDate || !customEndDate) {
                    alert('يرجى تحديد تاريخ البداية والانتهاء للمدة المخصصة');
                    return;
                }
                
                startDate = new Date(customStartDate);
                endDate = new Date(customEndDate);
            } else {
                const months = parseInt(duration);
                endDate.setMonth(endDate.getMonth() + months);
            }

            // توليد معرف فريد للترخيص
            const licenseId = 'LIC_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            // إنشاء بيانات الترخيص
            const licenseData = {
                licenseId: licenseId,
                customerName: customerName,
                customerPhone: customerPhone,
                customerEmail: document.getElementById('customerEmail').value.trim(),
                customerCompany: document.getElementById('customerCompany').value.trim(),
                licenseType: licenseType,
                startDate: startDate.toISOString(),
                expirationDate: endDate.toISOString(),
                generatedDate: new Date().toISOString(),
                notes: document.getElementById('notes').value.trim(),
                version: '1.1'
            };

            // تشفير البيانات
            const deviceKey = generateDefaultDeviceKey();
            const encryptedLicense = encrypt(JSON.stringify(licenseData), deviceKey);

            // عرض الترخيص
            document.getElementById('generatedLicense').textContent = encryptedLicense;
            document.getElementById('licenseOutput').style.display = 'block';
            
            // عرض معلومات الترخيص
            document.getElementById('infoLicenseId').textContent = licenseId;
            document.getElementById('infoCustomerName').textContent = customerName;
            document.getElementById('infoCustomerPhone').textContent = customerPhone;
            document.getElementById('infoLicenseType').textContent = licenseType === 'full' ? 'ترخيص كامل' : 'ترخيص تجريبي';
            document.getElementById('infoStartDate').textContent = startDate.toLocaleDateString('ar-EG');
            document.getElementById('infoEndDate').textContent = endDate.toLocaleDateString('ar-EG');

            const durationText = duration === 'custom' ? 'مدة مخصصة' : `${duration} شهر`;
            document.getElementById('infoDuration').textContent = durationText;
            
            document.getElementById('licenseInfo').style.display = 'block';

            currentLicenseData = licenseData;

            alert('تم توليد الترخيص بنجاح!');

            } catch (error) {
                console.error('خطأ في توليد الترخيص:', error);
                alert('حدث خطأ أثناء توليد الترخيص: ' + error.message);
            }
        }

        // نسخ الترخيص
        function copyLicense() {
            const licenseText = document.getElementById('generatedLicense').textContent;

            if (!licenseText) {
                alert('لا يوجد ترخيص للنسخ');
                return;
            }

            // محاولة استخدام Clipboard API الحديث
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(licenseText).then(() => {
                    alert('تم نسخ الترخيص إلى الحافظة بنجاح');
                }).catch((err) => {
                    console.error('فشل في نسخ الترخيص:', err);
                    fallbackCopyTextToClipboard(licenseText);
                });
            } else {
                // طريقة بديلة للمتصفحات القديمة
                fallbackCopyTextToClipboard(licenseText);
            }
        }

        // طريقة بديلة للنسخ
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    alert('تم نسخ الترخيص إلى الحافظة بنجاح');
                } else {
                    alert('فشل في نسخ الترخيص. يرجى نسخه يدوياً');
                }
            } catch (err) {
                console.error('فشل في نسخ الترخيص:', err);
                alert('فشل في نسخ الترخيص. يرجى نسخه يدوياً');
            }

            document.body.removeChild(textArea);
        }

        // حفظ الترخيص كملف
        function saveLicenseFile() {
            if (!currentLicenseData) {
                alert('لا يوجد ترخيص لحفظه');
                return;
            }

            const licenseText = document.getElementById('generatedLicense').textContent;
            const fileName = `license_${currentLicenseData.customerName}_${new Date().toISOString().split('T')[0]}.txt`;
            
            const blob = new Blob([licenseText], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // اختبار الترخيص
        function testLicense() {
            const licenseText = document.getElementById('generatedLicense').textContent;

            if (!licenseText) {
                alert('لا يوجد ترخيص للاختبار');
                return;
            }

            // محاولة فك التشفير للتأكد من صحة الترخيص
            try {
                const deviceKey = generateDefaultDeviceKey();
                const decrypted = decrypt(licenseText, deviceKey);

                if (!decrypted) {
                    alert('خطأ في فك تشفير الترخيص');
                    return;
                }

                const testData = JSON.parse(decrypted);

                alert('الترخيص صحيح ويمكن قراءته بنجاح!\n\nاسم العميل: ' + testData.customerName + '\nتاريخ الانتهاء: ' + new Date(testData.expirationDate).toLocaleDateString('ar-EG'));
            } catch (e) {
                alert('خطأ في الترخيص: ' + e.message);
            }
        }

        // مسح النموذج
        function clearForm() {
            document.getElementById('licenseForm').reset();
            document.getElementById('licenseOutput').style.display = 'none';
            document.getElementById('licenseInfo').style.display = 'none';
            document.getElementById('customDateRow').style.display = 'none';
            currentLicenseData = null;
        }
    </script>
</body>
</html>
