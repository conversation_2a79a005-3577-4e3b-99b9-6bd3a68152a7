<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الخروج التلقائي عند انتهاء الاشتراك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 900px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .countdown-demo {
            background-color: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-size: 18px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .danger-btn {
            background-color: #dc3545;
        }
        .danger-btn:hover {
            background-color: #c82333;
        }
        .success-btn {
            background-color: #28a745;
        }
        .success-btn:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⏰ اختبار الخروج التلقائي عند انتهاء الاشتراك</h1>
        
        <div class="test-section info">
            <h3>📋 الميزة الجديدة:</h3>
            <ul>
                <li><strong>خروج تلقائي بعد 10 ثواني:</strong> عند انتهاء صلاحية الاشتراك</li>
                <li><strong>عد تنازلي مرئي:</strong> إشعار يظهر العد التنازلي للمستخدم</li>
                <li><strong>إمكانية التجديد:</strong> زر "تجديد الآن" لإيقاف العد التنازلي</li>
                <li><strong>خروج آمن:</strong> إغلاق جميع الجلسات وإظهار شاشة تسجيل الدخول</li>
            </ul>
        </div>

        <div class="test-section warning">
            <h3>⚠️ كيفية الاختبار:</h3>
            <ol>
                <li>افتح النظام الرئيسي (index.html)</li>
                <li>ابدأ فترة تجريبية أو استخدم ترخيص منتهي الصلاحية</li>
                <li>انتظر حتى انتهاء الصلاحية أو استخدم أدوات المطور لمحاكاة الانتهاء</li>
                <li>ستظهر رسالة العد التنازلي تلقائياً</li>
                <li>يمكنك إما الانتظار للخروج التلقائي أو الضغط على "تجديد الآن"</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🧪 محاكاة العد التنازلي:</h3>
            <p>اضغط على الزر أدناه لمحاكاة العد التنازلي:</p>
            
            <button onclick="simulateCountdown()" class="danger-btn">
                <i class="bi bi-stopwatch"></i> محاكاة العد التنازلي
            </button>
            
            <button onclick="stopCountdown()" class="success-btn">
                <i class="bi bi-stop-circle"></i> إيقاف العد التنازلي
            </button>

            <div id="countdownDemo" class="countdown-demo" style="display: none;">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <strong>انتهت صلاحية الاشتراك!</strong><br>
                <span id="demoCountdownText">سيتم تسجيل الخروج تلقائياً خلال 10 ثانية</span>
            </div>
        </div>

        <div class="test-section success">
            <h3>✅ السيناريوهات المختبرة:</h3>
            <ul>
                <li><strong>انتهاء الفترة التجريبية:</strong> عد تنازلي 10 ثواني ثم خروج</li>
                <li><strong>انتهاء الاشتراك المدفوع:</strong> عد تنازلي 10 ثواني ثم خروج</li>
                <li><strong>تجديد أثناء العد التنازلي:</strong> إلغاء العد التنازلي والبقاء في النظام</li>
                <li><strong>إغلاق الإشعار:</strong> العد التنازلي يستمر في الخلفية</li>
            </ul>
        </div>

        <div class="test-section error">
            <h3>🚨 أدوات المطور للاختبار:</h3>
            <p>استخدم هذه الأوامر في console المتصفح:</p>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;">
// محاكاة انتهاء الاشتراك<br>
licenseSystem.licenseData.expirationDate = new Date(Date.now() - 1000).toISOString();<br>
licenseSystem.saveLicenseData();<br>
licenseSystem.performQuickLicenseCheck();<br><br>

// بدء العد التنازلي يدوياً<br>
licenseSystem.startAutoLogoutCountdown();<br><br>

// إلغاء العد التنازلي<br>
licenseSystem.cancelAutoLogoutCountdown();<br><br>

// فحص حالة الترخيص<br>
licenseSystem.checkLicenseStatus();
            </div>
        </div>

        <div class="test-section info">
            <h3>🎯 النتيجة المتوقعة:</h3>
            <ul>
                <li>عند انتهاء الاشتراك، يظهر إشعار أحمر مع العد التنازلي</li>
                <li>العد التنازلي يبدأ من 10 ثواني وينقص كل ثانية</li>
                <li>يمكن للمستخدم الضغط على "تجديد الآن" لإيقاف العد التنازلي</li>
                <li>بعد انتهاء العد التنازلي، يتم تسجيل الخروج تلقائياً</li>
                <li>يتم إظهار شاشة تسجيل الدخول مع نافذة الترخيص</li>
            </ul>
        </div>

        <button onclick="window.open('index.html', '_blank')" class="success-btn">
            🚀 اختبار النظام الآن
        </button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let demoTimer = null;
        let demoCountdown = 10;

        function simulateCountdown() {
            const demoDiv = document.getElementById('countdownDemo');
            const demoText = document.getElementById('demoCountdownText');
            
            demoDiv.style.display = 'block';
            demoCountdown = 10;
            
            if (demoTimer) clearInterval(demoTimer);
            
            demoTimer = setInterval(() => {
                demoCountdown--;
                if (demoCountdown > 0) {
                    demoText.textContent = `سيتم تسجيل الخروج تلقائياً خلال ${demoCountdown} ثانية`;
                } else {
                    demoText.textContent = 'تم تسجيل الخروج!';
                    clearInterval(demoTimer);
                    setTimeout(() => {
                        demoDiv.style.display = 'none';
                        alert('تم محاكاة تسجيل الخروج التلقائي!');
                    }, 1000);
                }
            }, 1000);
        }

        function stopCountdown() {
            if (demoTimer) {
                clearInterval(demoTimer);
                demoTimer = null;
            }
            document.getElementById('countdownDemo').style.display = 'none';
            alert('تم إيقاف العد التنازلي (محاكاة التجديد)');
        }

        // معلومات النظام
        console.log('=== اختبار الخروج التلقائي ===');
        console.log('✅ عد تنازلي 10 ثواني عند انتهاء الاشتراك');
        console.log('✅ إشعار مرئي مع إمكانية التجديد');
        console.log('✅ خروج تلقائي آمن');
        console.log('✅ إلغاء العد التنازلي عند التجديد');
    </script>
</body>
</html>
