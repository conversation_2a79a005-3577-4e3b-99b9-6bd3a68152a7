<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام منع إعادة استخدام مفاتيح الترخيص</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .result-box {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn-test { margin: 5px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="bi bi-shield-check"></i>
            اختبار نظام منع إعادة استخدام مفاتيح الترخيص
        </h1>

        <!-- قسم إنشاء مفتاح تجريبي -->
        <div class="test-section">
            <h3>1. إنشاء مفتاح ترخيص تجريبي</h3>
            <div class="row">
                <div class="col-md-6">
                    <input type="text" class="form-control mb-2" id="customerName" placeholder="اسم العميل" value="عميل تجريبي">
                    <input type="text" class="form-control mb-2" id="customerPhone" placeholder="رقم الهاتف" value="01234567890">
                    <input type="email" class="form-control mb-2" id="customerEmail" placeholder="البريد الإلكتروني" value="<EMAIL>">
                </div>
                <div class="col-md-6">
                    <select class="form-control mb-2" id="licenseType">
                        <option value="full">ترخيص كامل</option>
                        <option value="trial">فترة تجريبية</option>
                    </select>
                    <input type="number" class="form-control mb-2" id="validityDays" placeholder="مدة الصلاحية (أيام)" value="365">
                </div>
            </div>
            <button class="btn btn-primary btn-test" onclick="generateTestLicense()">إنشاء مفتاح تجريبي</button>
            <div id="generatedLicenseResult"></div>
        </div>

        <!-- قسم اختبار التفعيل الأول -->
        <div class="test-section">
            <h3>2. اختبار التفعيل الأول (يجب أن ينجح)</h3>
            <div class="input-group mb-3">
                <input type="text" class="form-control" id="testLicenseKey1" placeholder="أدخل مفتاح الترخيص هنا">
                <button class="btn btn-success" onclick="testFirstActivation()">تفعيل للمرة الأولى</button>
            </div>
            <div id="firstActivationResult"></div>
        </div>

        <!-- قسم اختبار إعادة التفعيل -->
        <div class="test-section">
            <h3>3. اختبار إعادة التفعيل (يجب أن يفشل)</h3>
            <div class="input-group mb-3">
                <input type="text" class="form-control" id="testLicenseKey2" placeholder="أدخل نفس مفتاح الترخيص">
                <button class="btn btn-warning" onclick="testSecondActivation()">محاولة إعادة التفعيل</button>
            </div>
            <div id="secondActivationResult"></div>
        </div>

        <!-- قسم عرض المفاتيح المستخدمة -->
        <div class="test-section">
            <h3>4. المفاتيح المستخدمة</h3>
            <button class="btn btn-info btn-test" onclick="showUsedKeys()">عرض المفاتيح المستخدمة</button>
            <button class="btn btn-danger btn-test" onclick="clearUsedKeys()">مسح جميع المفاتيح المستخدمة</button>
            <div id="usedKeysResult"></div>
        </div>

        <!-- قسم حالة النظام -->
        <div class="test-section">
            <h3>5. حالة النظام الحالية</h3>
            <button class="btn btn-secondary btn-test" onclick="showSystemStatus()">عرض حالة النظام</button>
            <div id="systemStatusResult"></div>
        </div>
    </div>

    <script src="js/license-system.js"></script>
    <script>
        let testLicenseKey = '';

        // تشفير البيانات (نفس الطريقة المستخدمة في النظام)
        function encrypt(text, key) {
            let result = '';
            for (let i = 0; i < text.length; i++) {
                result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
            }
            return btoa(result);
        }

        // توليد مفتاح تشفير للجهاز
        function generateDeviceKey() {
            const userAgent = navigator.userAgent;
            const language = navigator.language;
            const platform = navigator.platform;
            const screenRes = screen.width + 'x' + screen.height;
            return btoa(userAgent + language + platform + screenRes).substring(0, 32);
        }

        // إنشاء مفتاح ترخيص تجريبي
        function generateTestLicense() {
            const customerName = document.getElementById('customerName').value;
            const customerPhone = document.getElementById('customerPhone').value;
            const customerEmail = document.getElementById('customerEmail').value;
            const licenseType = document.getElementById('licenseType').value;
            const validityDays = parseInt(document.getElementById('validityDays').value);

            if (!customerName || !customerPhone) {
                showResult('generatedLicenseResult', 'يرجى ملء البيانات المطلوبة', 'error');
                return;
            }

            const currentDate = new Date();
            const expirationDate = new Date(currentDate.getTime() + (validityDays * 24 * 60 * 60 * 1000));

            const licenseData = {
                customerName: customerName,
                customerPhone: customerPhone,
                customerEmail: customerEmail,
                licenseType: licenseType,
                expirationDate: expirationDate.toISOString(),
                licenseId: 'TEST_' + Date.now(),
                generatedDate: currentDate.toISOString()
            };

            const deviceKey = generateDeviceKey();
            testLicenseKey = encrypt(JSON.stringify(licenseData), deviceKey);

            document.getElementById('testLicenseKey1').value = testLicenseKey;
            document.getElementById('testLicenseKey2').value = testLicenseKey;

            showResult('generatedLicenseResult', 
                `تم إنشاء مفتاح الترخيص بنجاح:<br>
                <small style="word-break: break-all; background: #f0f0f0; padding: 10px; display: block; margin: 10px 0; border-radius: 5px;">
                ${testLicenseKey}
                </small>
                <strong>بيانات الترخيص:</strong><br>
                العميل: ${customerName}<br>
                الهاتف: ${customerPhone}<br>
                النوع: ${licenseType}<br>
                صالح حتى: ${expirationDate.toLocaleDateString('ar-EG')}`, 'success');
        }

        // اختبار التفعيل الأول
        function testFirstActivation() {
            const licenseKey = document.getElementById('testLicenseKey1').value.trim();
            if (!licenseKey) {
                showResult('firstActivationResult', 'يرجى إدخال مفتاح الترخيص', 'error');
                return;
            }

            const result = licenseSystem.activateLicense(licenseKey);
            
            if (result.success) {
                showResult('firstActivationResult', 
                    `✅ نجح التفعيل الأول كما هو متوقع!<br>
                    الرسالة: ${result.message}<br>
                    نوع الترخيص: ${result.licenseType}`, 'success');
            } else {
                showResult('firstActivationResult', 
                    `❌ فشل التفعيل الأول (غير متوقع):<br>
                    ${result.message}`, 'error');
            }
        }

        // اختبار إعادة التفعيل
        function testSecondActivation() {
            const licenseKey = document.getElementById('testLicenseKey2').value.trim();
            if (!licenseKey) {
                showResult('secondActivationResult', 'يرجى إدخال مفتاح الترخيص', 'error');
                return;
            }

            const result = licenseSystem.activateLicense(licenseKey);
            
            if (!result.success && result.message.includes('تم استخدام هذا المفتاح من قبل')) {
                showResult('secondActivationResult', 
                    `✅ تم منع إعادة التفعيل بنجاح!<br>
                    الرسالة: ${result.message}<br>
                    <strong>النظام يعمل بشكل صحيح!</strong>`, 'success');
            } else if (result.success) {
                showResult('secondActivationResult', 
                    `❌ خطأ: تم السماح بإعادة التفعيل (يجب منعه)!<br>
                    الرسالة: ${result.message}`, 'error');
            } else {
                showResult('secondActivationResult', 
                    `⚠️ فشل لسبب آخر:<br>
                    ${result.message}`, 'info');
            }
        }

        // عرض المفاتيح المستخدمة
        function showUsedKeys() {
            const usedKeys = licenseSystem.loadUsedLicenseKeys();
            
            if (usedKeys.length === 0) {
                showResult('usedKeysResult', 'لا توجد مفاتيح مستخدمة', 'info');
                return;
            }

            let html = `<strong>المفاتيح المستخدمة (${usedKeys.length}):</strong><br>`;
            usedKeys.forEach((key, index) => {
                html += `
                    <div style="background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 4px solid #007bff;">
                        <strong>مفتاح ${index + 1}:</strong><br>
                        معرف الترخيص: ${key.licenseId}<br>
                        تاريخ الاستخدام: ${new Date(key.usedDate).toLocaleString('ar-EG')}<br>
                        Hash: ${key.hash}
                    </div>
                `;
            });
            
            showResult('usedKeysResult', html, 'info');
        }

        // مسح المفاتيح المستخدمة
        function clearUsedKeys() {
            if (confirm('هل أنت متأكد من مسح جميع المفاتيح المستخدمة؟')) {
                licenseSystem.saveUsedLicenseKeys([]);
                showResult('usedKeysResult', 'تم مسح جميع المفاتيح المستخدمة', 'success');
            }
        }

        // عرض حالة النظام
        function showSystemStatus() {
            const status = licenseSystem.checkLicenseStatus();
            const licenseData = licenseSystem.licenseData;
            
            let html = `
                <strong>حالة النظام:</strong><br>
                الحالة: ${status.status}<br>
                الرسالة: ${status.message}<br>
                مفعل: ${licenseData.isActivated ? 'نعم' : 'لا'}<br>
                نوع الترخيص: ${licenseData.licenseType || 'غير محدد'}<br>
            `;
            
            if (licenseData.customerInfo) {
                html += `
                    <strong>معلومات العميل:</strong><br>
                    الاسم: ${licenseData.customerInfo.name}<br>
                    الهاتف: ${licenseData.customerInfo.phone}<br>
                `;
            }
            
            showResult('systemStatusResult', html, 'info');
        }

        // عرض النتائج
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result-box ${type}">${message}</div>`;
        }

        // تحديث الصفحة عند التحميل
        window.onload = function() {
            showSystemStatus();
        };
    </script>
</body>
</html>
