<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رسم الحضور الأسبوعي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .chart-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            color: white;
        }
        
        .bg-success { background-color: #28a745; }
        .bg-danger { background-color: #dc3545; }
        .bg-warning { background-color: #ffc107; }
        .bg-info { background-color: #17a2b8; }
        
        .btn-test {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            margin: 5px;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="bi bi-bar-chart-fill me-2"></i>
            اختبار رسم الحضور الأسبوعي
        </h1>
        
        <!-- إحصائيات الحضور -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="bi bi-person-check"></i>
                    </div>
                    <h3 id="todayPresent">0</h3>
                    <p>حاضر اليوم</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-danger">
                        <i class="bi bi-person-x"></i>
                    </div>
                    <h3 id="todayAbsent">0</h3>
                    <p>غائب اليوم</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="bi bi-clock"></i>
                    </div>
                    <h3 id="todayLate">0</h3>
                    <p>متأخر اليوم</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="bi bi-door-open"></i>
                    </div>
                    <h3 id="todayLeft">0</h3>
                    <p>انصرف اليوم</p>
                </div>
            </div>
        </div>
        
        <!-- أزرار الاختبار -->
        <div class="text-center mb-4">
            <button class="btn btn-test" onclick="generateTestData()">
                <i class="bi bi-database-fill me-2"></i>
                إنشاء بيانات تجريبية
            </button>
            <button class="btn btn-test" onclick="updateChart()">
                <i class="bi bi-arrow-clockwise me-2"></i>
                تحديث الرسم البياني
            </button>
            <button class="btn btn-test" onclick="clearData()">
                <i class="bi bi-trash me-2"></i>
                مسح البيانات
            </button>
        </div>
        
        <!-- الرسم البياني -->
        <div class="chart-container">
            <h4 class="text-center mb-3">
                <i class="bi bi-calendar-week me-2"></i>
                إحصائيات الحضور الأسبوعية
            </h4>
            <canvas id="attendanceChart" width="400" height="200"></canvas>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="alert alert-info mt-4">
            <h5><i class="bi bi-info-circle me-2"></i>معلومات الاختبار:</h5>
            <ul class="mb-0">
                <li>يعرض الرسم البياني إحصائيات الحضور للأيام السبعة الماضية</li>
                <li>يتم تحديث البيانات تلقائياً عند تغيير بيانات الحضور</li>
                <li>الرسم البياني يدعم التفاعل والتكبير</li>
                <li>البيانات محفوظة في localStorage</li>
            </ul>
        </div>
    </div>

    <script>
        // بيانات تجريبية للعمال
        const testWorkers = [
            { id: 'w1', name: 'أحمد محمد', status: 'active' },
            { id: 'w2', name: 'فاطمة علي', status: 'active' },
            { id: 'w3', name: 'محمود حسن', status: 'active' },
            { id: 'w4', name: 'نور الدين', status: 'active' },
            { id: 'w5', name: 'سارة أحمد', status: 'active' }
        ];

        let attendanceChart = null;
        let attendanceData = {};

        // إنشاء بيانات تجريبية
        function generateTestData() {
            attendanceData = {};
            const today = new Date();
            
            // إنشاء بيانات للأيام السبعة الماضية
            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(today.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];
                
                attendanceData[dateStr] = {};
                
                testWorkers.forEach(worker => {
                    const random = Math.random();
                    let status = 'present';
                    
                    if (random < 0.1) status = 'absent';
                    else if (random < 0.2) status = 'late';
                    
                    attendanceData[dateStr][worker.id] = {
                        status: status,
                        arrivalTime: '08:00',
                        leaveTime: status !== 'absent' ? '17:00' : ''
                    };
                });
            }
            
            localStorage.setItem('test_attendance', JSON.stringify(attendanceData));
            updateStats();
            updateChart();
            
            alert('تم إنشاء بيانات تجريبية بنجاح!');
        }

        // تحديث الإحصائيات
        function updateStats() {
            const today = new Date().toISOString().split('T')[0];
            const todayData = attendanceData[today] || {};
            
            let present = 0, absent = 0, late = 0, left = 0;
            
            testWorkers.forEach(worker => {
                const attendance = todayData[worker.id];
                if (attendance) {
                    if (attendance.status === 'present') {
                        present++;
                        if (attendance.leaveTime) left++;
                    } else if (attendance.status === 'late') {
                        late++;
                        if (attendance.leaveTime) left++;
                    } else {
                        absent++;
                    }
                } else {
                    absent++;
                }
            });
            
            document.getElementById('todayPresent').textContent = present;
            document.getElementById('todayAbsent').textContent = absent;
            document.getElementById('todayLate').textContent = late;
            document.getElementById('todayLeft').textContent = left;
        }

        // حساب إحصائيات الحضور الأسبوعية
        function calculateWeeklyStats() {
            const today = new Date();
            const weeklyData = {};
            
            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(today.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];
                const dayName = date.toLocaleDateString('ar-EG', { weekday: 'long' });
                
                weeklyData[dateStr] = {
                    dayName: dayName,
                    present: 0,
                    absent: 0,
                    late: 0
                };
                
                const dayData = attendanceData[dateStr] || {};
                testWorkers.forEach(worker => {
                    const attendance = dayData[worker.id];
                    const status = attendance ? attendance.status : 'absent';
                    
                    if (status === 'present') {
                        weeklyData[dateStr].present++;
                    } else if (status === 'late') {
                        weeklyData[dateStr].late++;
                    } else {
                        weeklyData[dateStr].absent++;
                    }
                });
            }
            
            return weeklyData;
        }

        // تحديث الرسم البياني
        function updateChart() {
            const ctx = document.getElementById('attendanceChart').getContext('2d');
            
            if (attendanceChart) {
                attendanceChart.destroy();
            }
            
            const weeklyStats = calculateWeeklyStats();
            const labels = [];
            const presentData = [];
            const absentData = [];
            const lateData = [];
            
            Object.keys(weeklyStats).forEach(date => {
                const data = weeklyStats[date];
                labels.push(data.dayName);
                presentData.push(data.present);
                absentData.push(data.absent);
                lateData.push(data.late);
            });
            
            attendanceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'حاضر',
                            data: presentData,
                            backgroundColor: 'rgba(39, 174, 96, 0.8)',
                            borderColor: 'rgba(39, 174, 96, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'غائب',
                            data: absentData,
                            backgroundColor: 'rgba(231, 76, 60, 0.8)',
                            borderColor: 'rgba(231, 76, 60, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'متأخر',
                            data: lateData,
                            backgroundColor: 'rgba(243, 156, 18, 0.8)',
                            borderColor: 'rgba(243, 156, 18, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        title: {
                            display: true,
                            text: 'إحصائيات الحضور الأسبوعية'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: testWorkers.length
                        }
                    }
                }
            });
        }

        // مسح البيانات
        function clearData() {
            attendanceData = {};
            localStorage.removeItem('test_attendance');
            updateStats();
            if (attendanceChart) {
                attendanceChart.destroy();
                attendanceChart = null;
            }
            alert('تم مسح البيانات!');
        }

        // تحميل البيانات عند بدء التشغيل
        window.onload = function() {
            const savedData = localStorage.getItem('test_attendance');
            if (savedData) {
                attendanceData = JSON.parse(savedData);
                updateStats();
                updateChart();
            }
        };
    </script>
</body>
</html>
