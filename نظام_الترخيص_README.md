# نظام الدفع المحلي بدون إنترنت

## نظرة عامة
تم تطوير نظام دفع محلي متكامل يعمل بدون إنترنت لإدارة تراخيص البرنامج مع دعم الفترة التجريبية والاشتراكات السنوية.

## الميزات الرئيسية

### 🔐 نظام الترخيص
- **ترخيص كامل**: اشتراك سنوي مع جميع الميزات
- **فترة تجريبية**: 24 ساعة فقط للتجربة
- **حماية من الاستخدام المتكرر**: كل مفتاح ترخيص يُستخدم مرة واحدة فقط
- **تشفير محلي**: حماية البيانات باستخدام مفتاح الجهاز
- **🚫 لا يوجد وضع مجاني**: يجب تفعيل ترخيص صالح للوصول للنظام

### 🎯 تدفق العملية
1. **العميل يتواصل مع المطور** (هاتف/واتساب: 01100693019)
2. **المطور يولد ترخيص مخصص** باستخدام `license-generator.html`
3. **يرسل الترخيص للعميل** (رسالة/ملف)
4. **العميل يدخل الترخيص في النظام**
5. **الترخيص يعمل مرة واحدة فقط**

### 📱 واجهة المستخدم
- **زر مفتاح الترخيص**: بجانب تسجيل الدخول مع تغيير اللون حسب الحالة
  - 🟡 أصفر: غير مفعل
  - 🟢 أخضر: مشترك
  - 🟠 برتقالي: ينتهي قريباً
  - 🔴 أحمر: منتهي الصلاحية
- **بطاقة معلومات المشترك**: في لوحة التحكم مع جميع التفاصيل
- **نظام إشعارات**: تحذيرات انتهاء الاشتراك في آخر 30 يوم
- **منع الوصول المجاني**: لا يمكن الوصول للنظام بدون ترخيص صالح

## الملفات المضافة/المحدثة

### 1. `license-generator.html`
أداة المطور لتوليد التراخيص (موجودة مسبقاً)
- توليد تراخيص مخصصة للعملاء
- دعم الفترة التجريبية والتراخيص الكاملة
- تشفير آمن للبيانات

### 2. `js/license-system.js`
نظام إدارة التراخيص الرئيسي (محدث)
- إدارة حالات الترخيص المختلفة
- نظام التحذيرات والإشعارات
- حماية من الاستخدام المتكرر للمفاتيح

### 3. `index.html`
الملف الرئيسي (محدث)
- إضافة زر مفتاح الترخيص
- نوافذ إدارة الترخيص
- بطاقة معلومات المشترك في لوحة التحكم

### 4. `css/style.css`
الأنماط (محدث)
- أنماط زر مفتاح الترخيص
- أنماط نوافذ الترخيص
- أنماط بطاقة معلومات المشترك

### 5. `test-license-system.html`
صفحة اختبار النظام (جديد)
- اختبار جميع وظائف النظام
- محاكاة انتهاء الفترة التجريبية
- عرض حالة النظام في الوقت الفعلي

## كيفية الاستخدام

### للمطور:
1. افتح `license-generator.html`
2. أدخل بيانات العميل
3. اختر نوع الترخيص (كامل/تجريبي)
4. حدد مدة الترخيص
5. اضغط "توليد الترخيص"
6. انسخ المفتاح وأرسله للعميل

### للعميل:
1. افتح البرنامج
2. اضغط على زر "مفتاح الترخيص"
3. الصق المفتاح المستلم من المطور
4. اضغط "تفعيل الترخيص"
5. أو اضغط "فترة تجريبية (24 ساعة)" للتجربة

## حالات الترخيص

### 🆓 مجاني (Free)
- جميع الميزات متاحة
- لا توجد قيود زمنية
- يمكن الترقية للاشتراك المدفوع

### 🧪 فترة تجريبية (Trial)
- مدة 24 ساعة فقط
- جميع الميزات متاحة
- تحذيرات في آخر 6 ساعات
- نافذة الترخيص تظهر عند الانتهاء

### ✅ مشترك (Active)
- اشتراك سنوي كامل
- جميع الميزات متاحة
- تحذيرات في آخر 30 يوم

### ⚠️ ينتهي قريباً (Warning)
- آخر 30 يوم من الاشتراك
- تحذيرات يومية
- إمكانية التجديد

### ❌ منتهي الصلاحية (Expired)
- انتهى الاشتراك
- التحويل للوضع المجاني
- إمكانية التجديد

## الأمان والحماية

### 🔒 تشفير البيانات
- تشفير محلي باستخدام مفتاح الجهاز
- حماية من التلاعب في البيانات
- تخزين آمن في localStorage

### 🛡️ حماية من الاستخدام المتكرر
- كل مفتاح ترخيص يُستخدم مرة واحدة فقط
- تتبع المفاتيح المستخدمة
- منع إعادة استخدام المفاتيح

### 🔐 ربط بالجهاز
- المفتاح مرتبط بالجهاز المحدد
- لا يمكن استخدامه على أجهزة أخرى
- حماية من النسخ غير المصرح به

## الاختبار والتطوير

### اختبار النظام:
1. افتح `test-license-system.html`
2. راقب حالة النظام
3. اختبر الفترة التجريبية
4. اختبر انتهاء الصلاحية
5. تحقق من الإشعارات

### أدوات التطوير:
- سجل الأحداث في الوقت الفعلي
- محاكاة انتهاء الفترة التجريبية
- مسح البيانات للاختبار
- تحديث الحالة التلقائي

## معلومات الاتصال
**المطور**: البشمهندس أحمد يونس  
**الهاتف**: 01100693019  
**للحصول على التراخيص**: اتصل بالرقم أعلاه

## 🆕 التحديثات الجديدة - إلغاء الوضع المجاني

### ما تم تغييره:
- **❌ إلغاء الوضع المجاني نهائياً**: لا يمكن الوصول للنظام بدون ترخيص صالح
- **🔒 منع الوصول بدون ترخيص**: النظام يمنع الوصول تماماً بدون تفعيل
- **⏰ منع الوصول بترخيص منتهي**: عند انتهاء الترخيص يتم منع الوصول فوراً
- **🚫 منع الوصول بفترة تجريبية منتهية**: لا يمكن الاستمرار بعد انتهاء الـ24 ساعة

### الحالات المدعومة الآن:
1. **غير مفعل** (`inactive`): منع الوصول + رسالة تطلب التفعيل
2. **فترة تجريبية** (`trial`): 24 ساعة فقط
3. **مشترك** (`active`): وصول كامل
4. **تحذير انتهاء** (`warning`): آخر 30 يوم
5. **منتهي الصلاحية** (`expired`): منع الوصول + رسالة تطلب التجديد

### ملفات الاختبار:
- `test-no-free-access.html`: اختبار منع الوصول المجاني
- `test-license-usage.html`: اختبار منع إعادة استخدام المفاتيح

## ملاحظات مهمة
- النظام يعمل بدون إنترنت بالكامل
- جميع البيانات محفوظة محلياً
- النظام آمن ومحمي من التلاعب
- دعم فني متاح عبر الهاتف
- التحديثات متاحة من المطور
- **🔴 لا يوجد وضع مجاني - يجب تفعيل ترخيص للوصول**

---
*تم تطوير هذا النظام خصيصاً لإدارة مصنع الشنط مع أعلى معايير الأمان والحماية*
