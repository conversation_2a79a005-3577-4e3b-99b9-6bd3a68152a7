<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إحصائيات السحب</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .stat-display {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        .test-button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>اختبار إحصائيات الكمية المسحوبة</h2>
        
        <div class="stat-display">
            <strong>إجمالي الكمية المسحوبة:</strong> 
            <span id="totalWithdrawn">0</span>
        </div>
        
        <div class="stat-display">
            <strong>عدد سجلات السحب:</strong> 
            <span id="withdrawalCount">0</span>
        </div>
        
        <div class="stat-display">
            <strong>إجمالي القيمة المسحوبة:</strong> 
            <span id="totalValue">0</span> ج.م
        </div>
        
        <button class="test-button" onclick="loadTestData()">تحميل بيانات تجريبية</button>
        <button class="test-button" onclick="clearTestData()">مسح البيانات</button>
        <button class="test-button" onclick="updateStats()">تحديث الإحصائيات</button>
        
        <div id="testResults" style="margin-top: 20px;"></div>
    </div>

    <script>
        // محاكاة بيانات سحب العمال
        function loadTestData() {
            const testWithdrawals = [
                {
                    id: '1',
                    workerName: 'أحمد محمد',
                    productName: 'شنطة جلد',
                    quantity: 10,
                    unitPrice: 50,
                    totalValue: 500,
                    date: '2024-01-15'
                },
                {
                    id: '2',
                    workerName: 'محمد علي',
                    productName: 'شنطة قماش',
                    quantity: 15,
                    unitPrice: 30,
                    totalValue: 450,
                    date: '2024-01-16'
                },
                {
                    id: '3',
                    workerName: 'علي حسن',
                    productName: 'شنطة رياضية',
                    quantity: 8,
                    unitPrice: 75,
                    totalValue: 600,
                    date: '2024-01-17'
                }
            ];
            
            localStorage.setItem('bagFactory_workerWithdrawals', JSON.stringify(testWithdrawals));
            updateStats();
            document.getElementById('testResults').innerHTML = '<div style="color: green;">تم تحميل البيانات التجريبية بنجاح!</div>';
        }
        
        function clearTestData() {
            localStorage.removeItem('bagFactory_workerWithdrawals');
            updateStats();
            document.getElementById('testResults').innerHTML = '<div style="color: red;">تم مسح البيانات!</div>';
        }
        
        function updateStats() {
            const saved = localStorage.getItem('bagFactory_workerWithdrawals');
            const withdrawals = saved ? JSON.parse(saved) : [];
            
            const totalQuantity = withdrawals.reduce((sum, w) => sum + w.quantity, 0);
            const totalValue = withdrawals.reduce((sum, w) => sum + w.totalValue, 0);
            const count = withdrawals.length;
            
            document.getElementById('totalWithdrawn').textContent = totalQuantity.toLocaleString();
            document.getElementById('withdrawalCount').textContent = count;
            document.getElementById('totalValue').textContent = totalValue.toLocaleString();
        }
        
        // تحديث الإحصائيات عند تحميل الصفحة
        updateStats();
    </script>
</body>
</html>
