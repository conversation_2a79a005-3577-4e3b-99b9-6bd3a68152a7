<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام مفاتيح الترخيص</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 900px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .danger-btn {
            background-color: #dc3545;
        }
        .danger-btn:hover {
            background-color: #c82333;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 اختبار نظام مفاتيح الترخيص</h1>
        
        <div class="test-section info">
            <h3>📋 كيف يعمل النظام:</h3>
            <ol>
                <li><strong>استخدام لمرة واحدة:</strong> كل مفتاح ترخيص يمكن استخدامه مرة واحدة فقط</li>
                <li><strong>تسجيل المفاتيح:</strong> عند التفعيل، يتم حفظ المفتاح في قائمة المستخدمة</li>
                <li><strong>منع إعادة الاستخدام:</strong> إذا حاول المستخدم استخدام نفس المفتاح مرة أخرى، يظهر خطأ</li>
                <li><strong>رسالة واضحة:</strong> "لقد تم استخدام هذا المفتاح من قبل ومنتهي الصلاحية"</li>
            </ol>
        </div>

        <div class="test-section warning">
            <h3>⚠️ اختبار المفاتيح:</h3>
            <p>لاختبار النظام، استخدم هذه الخطوات:</p>
            
            <div class="code-block">
1. افتح النظام الرئيسي (index.html)
2. اضغط على "مفتاح الترخيص"
3. جرب إدخال مفتاح وهمي مثل: TEST-KEY-12345
4. إذا تم قبوله، حاول إدخال نفس المفتاح مرة أخرى
5. يجب أن تظهر رسالة: "لقد تم استخدام هذا المفتاح من قبل ومنتهي الصلاحية"
            </div>
        </div>

        <div class="test-section success">
            <h3>✅ المميزات المحسنة:</h3>
            <ul>
                <li><strong>رسائل أوضح:</strong> رسائل خطأ مفصلة ومفهومة</li>
                <li><strong>تسجيل مفصل:</strong> حفظ تاريخ الاستخدام ومعلومات إضافية</li>
                <li><strong>حماية قوية:</strong> استخدام hash للمفاتيح لحماية إضافية</li>
                <li><strong>سجل كامل:</strong> تتبع جميع المفاتيح المستخدمة</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🛠️ أدوات المطور:</h3>
            <p>يمكن للمطورين استخدام هذه الأوامر في console المتصفح:</p>
            
            <button onclick="showUsedKeys()">عرض المفاتيح المستخدمة</button>
            <button onclick="testLicenseKey()" class="danger-btn">اختبار مفتاح وهمي</button>
            <button onclick="clearKeys()" class="danger-btn">مسح جميع المفاتيح (خطر!)</button>
            
            <div class="code-block">
// عرض المفاتيح المستخدمة
licenseSystem.showUsedLicenseKeys();

// مسح جميع المفاتيح (استخدم بحذر!)
licenseSystem.clearUsedLicenseKeys();

// التحقق من مفتاح معين
licenseSystem.isLicenseKeyUsed('مفتاحك-هنا');
            </div>
        </div>

        <div class="test-section error">
            <h3>🚨 تحذيرات مهمة:</h3>
            <ul>
                <li><strong>لا تشارك المفاتيح:</strong> كل مفتاح للاستخدام الشخصي فقط</li>
                <li><strong>احتفظ بنسخة احتياطية:</strong> احفظ مفتاحك في مكان آمن</li>
                <li><strong>لا تمسح البيانات:</strong> مسح localStorage سيفقد معلومات الترخيص</li>
                <li><strong>استخدم مرة واحدة:</strong> لا يمكن استرداد المفاتيح المستخدمة</li>
            </ul>
        </div>

        <button onclick="window.open('index.html', '_blank')">
            🚀 اختبار النظام الآن
        </button>
    </div>

    <script>
        // دوال الاختبار
        function showUsedKeys() {
            if (typeof licenseSystem !== 'undefined') {
                licenseSystem.showUsedLicenseKeys();
            } else {
                console.log('نظام الترخيص غير محمل. افتح index.html أولاً');
                alert('يجب فتح النظام الرئيسي (index.html) أولاً لاستخدام هذه الوظيفة');
            }
        }

        function testLicenseKey() {
            const testKey = 'TEST-KEY-' + Date.now();
            console.log('اختبار مفتاح وهمي:', testKey);
            alert('تم إنشاء مفتاح اختبار: ' + testKey + '\nانسخه واستخدمه في النظام الرئيسي');
        }

        function clearKeys() {
            if (typeof licenseSystem !== 'undefined') {
                licenseSystem.clearUsedLicenseKeys();
            } else {
                if (confirm('هل تريد مسح جميع المفاتيح المستخدمة؟ (هذا سيسمح بإعادة استخدام المفاتيح القديمة)')) {
                    localStorage.removeItem('bagFactory_usedKeys');
                    alert('تم مسح جميع المفاتيح المستخدمة');
                }
            }
        }

        // معلومات النظام
        console.log('=== نظام مفاتيح الترخيص ===');
        console.log('✅ كل مفتاح يستخدم مرة واحدة فقط');
        console.log('✅ حماية ضد إعادة الاستخدام');
        console.log('✅ رسائل خطأ واضحة');
        console.log('✅ تسجيل مفصل للاستخدام');
    </script>
</body>
</html>
