<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار منع الوصول المجاني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 900px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .result-box {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .btn-test { margin: 5px; }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-blocked { background-color: #dc3545; }
        .status-allowed { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4 text-danger">
            <i class="bi bi-shield-x"></i>
            اختبار منع الوصول المجاني
        </h1>
        
        <div class="alert alert-danger text-center">
            <strong>هذا الاختبار يتأكد من أن النظام لا يسمح بالوصول المجاني</strong><br>
            يجب أن يكون هناك ترخيص صالح للوصول للنظام
        </div>

        <!-- قسم حالة النظام الحالية -->
        <div class="test-section">
            <h3>1. حالة النظام الحالية</h3>
            <button class="btn btn-primary btn-test" onclick="checkCurrentStatus()">فحص الحالة الحالية</button>
            <div id="currentStatusResult"></div>
        </div>

        <!-- قسم اختبار الوصول بدون ترخيص -->
        <div class="test-section">
            <h3>2. اختبار الوصول بدون ترخيص</h3>
            <button class="btn btn-danger btn-test" onclick="testAccessWithoutLicense()">محاولة الوصول بدون ترخيص</button>
            <button class="btn btn-warning btn-test" onclick="clearAllLicenseData()">مسح جميع بيانات الترخيص</button>
            <div id="accessTestResult"></div>
        </div>

        <!-- قسم اختبار انتهاء الترخيص -->
        <div class="test-section">
            <h3>3. اختبار انتهاء الترخيص</h3>
            <button class="btn btn-warning btn-test" onclick="simulateExpiredLicense()">محاكاة ترخيص منتهي الصلاحية</button>
            <button class="btn btn-info btn-test" onclick="testAccessWithExpiredLicense()">اختبار الوصول بترخيص منتهي</button>
            <div id="expiredTestResult"></div>
        </div>

        <!-- قسم اختبار الفترة التجريبية المنتهية -->
        <div class="test-section">
            <h3>4. اختبار الفترة التجريبية المنتهية</h3>
            <button class="btn btn-secondary btn-test" onclick="simulateExpiredTrial()">محاكاة فترة تجريبية منتهية</button>
            <button class="btn btn-info btn-test" onclick="testAccessWithExpiredTrial()">اختبار الوصول بفترة تجريبية منتهية</button>
            <div id="trialTestResult"></div>
        </div>

        <!-- قسم إعادة تعيين النظام -->
        <div class="test-section">
            <h3>5. إعادة تعيين النظام</h3>
            <button class="btn btn-success btn-test" onclick="resetSystem()">إعادة تعيين النظام</button>
            <button class="btn btn-info btn-test" onclick="showLicenseModal()">فتح نافذة الترخيص</button>
            <div id="resetResult"></div>
        </div>

        <!-- قسم نتائج الاختبار -->
        <div class="test-section">
            <h3>6. ملخص نتائج الاختبار</h3>
            <div id="testSummary">
                <p class="text-muted">قم بتشغيل الاختبارات لرؤية النتائج</p>
            </div>
        </div>
    </div>

    <script src="js/license-system.js"></script>
    <script>
        let testResults = {
            noLicenseBlocked: false,
            expiredLicenseBlocked: false,
            expiredTrialBlocked: false,
            systemWorking: false
        };

        // فحص الحالة الحالية
        function checkCurrentStatus() {
            try {
                const status = licenseSystem.checkLicenseStatus();
                const access = licenseSystem.checkAccess();
                
                let statusIcon = '';
                let statusClass = '';
                
                if (access.allowed) {
                    statusIcon = '<span class="status-indicator status-allowed"></span>';
                    statusClass = 'success';
                } else {
                    statusIcon = '<span class="status-indicator status-blocked"></span>';
                    statusClass = 'error';
                }
                
                const result = `
                    ${statusIcon} <strong>حالة النظام:</strong><br>
                    الحالة: ${status.status}<br>
                    الرسالة: ${status.message}<br>
                    السماح بالوصول: ${access.allowed ? 'نعم' : 'لا'}<br>
                    مفعل: ${licenseSystem.licenseData.isActivated ? 'نعم' : 'لا'}<br>
                    نوع الترخيص: ${licenseSystem.licenseData.licenseType || 'غير محدد'}
                `;
                
                showResult('currentStatusResult', result, statusClass);
                testResults.systemWorking = true;
                updateTestSummary();
                
            } catch (error) {
                showResult('currentStatusResult', 'خطأ في فحص الحالة: ' + error.message, 'error');
            }
        }

        // اختبار الوصول بدون ترخيص
        function testAccessWithoutLicense() {
            try {
                const access = licenseSystem.checkAccess();
                
                if (!access.allowed) {
                    showResult('accessTestResult', 
                        `✅ ممتاز! النظام يمنع الوصول بدون ترخيص<br>
                        <span class="status-indicator status-blocked"></span>
                        الحالة: ${access.status}<br>
                        الرسالة: ${access.message}`, 'success');
                    testResults.noLicenseBlocked = true;
                } else {
                    showResult('accessTestResult', 
                        `❌ خطأ! النظام يسمح بالوصول بدون ترخيص<br>
                        <span class="status-indicator status-allowed"></span>
                        هذا يعني أن الوضع المجاني ما زال موجود!`, 'error');
                    testResults.noLicenseBlocked = false;
                }
                
                updateTestSummary();
                
            } catch (error) {
                showResult('accessTestResult', 'خطأ في الاختبار: ' + error.message, 'error');
            }
        }

        // مسح جميع بيانات الترخيص
        function clearAllLicenseData() {
            try {
                localStorage.removeItem('bagFactory_license');
                localStorage.removeItem('bagFactory_usedKeys');
                
                // إعادة تحميل بيانات النظام
                licenseSystem.licenseData = licenseSystem.loadLicenseData();
                
                showResult('accessTestResult', 'تم مسح جميع بيانات الترخيص', 'info');
                
                // اختبار فوري للوصول
                setTimeout(() => {
                    testAccessWithoutLicense();
                }, 500);
                
            } catch (error) {
                showResult('accessTestResult', 'خطأ في مسح البيانات: ' + error.message, 'error');
            }
        }

        // محاكاة ترخيص منتهي الصلاحية
        function simulateExpiredLicense() {
            try {
                const expiredDate = new Date();
                expiredDate.setDate(expiredDate.getDate() - 10); // منتهي منذ 10 أيام
                
                const expiredLicenseData = {
                    isActivated: true,
                    licenseType: 'full',
                    activationDate: new Date(expiredDate.getTime() - (365 * 24 * 60 * 60 * 1000)).toISOString(),
                    expirationDate: expiredDate.toISOString(),
                    customerInfo: {
                        name: 'عميل تجريبي',
                        phone: '01234567890',
                        email: '<EMAIL>'
                    },
                    trialStarted: false,
                    trialStartDate: null,
                    lastWarningDate: null,
                    usedLicenseKeys: []
                };
                
                localStorage.setItem('bagFactory_license', JSON.stringify(expiredLicenseData));
                licenseSystem.licenseData = expiredLicenseData;
                
                showResult('expiredTestResult', 'تم إنشاء ترخيص منتهي الصلاحية للاختبار', 'info');
                
            } catch (error) {
                showResult('expiredTestResult', 'خطأ في محاكاة الترخيص المنتهي: ' + error.message, 'error');
            }
        }

        // اختبار الوصول بترخيص منتهي
        function testAccessWithExpiredLicense() {
            try {
                const access = licenseSystem.checkAccess();
                
                if (!access.allowed) {
                    showResult('expiredTestResult', 
                        `✅ ممتاز! النظام يمنع الوصول بترخيص منتهي<br>
                        <span class="status-indicator status-blocked"></span>
                        الحالة: ${access.status}<br>
                        الرسالة: ${access.message}`, 'success');
                    testResults.expiredLicenseBlocked = true;
                } else {
                    showResult('expiredTestResult', 
                        `❌ خطأ! النظام يسمح بالوصول بترخيص منتهي<br>
                        <span class="status-indicator status-allowed"></span>
                        هذا يعني أن الوضع المجاني ما زال موجود!`, 'error');
                    testResults.expiredLicenseBlocked = false;
                }
                
                updateTestSummary();
                
            } catch (error) {
                showResult('expiredTestResult', 'خطأ في الاختبار: ' + error.message, 'error');
            }
        }

        // محاكاة فترة تجريبية منتهية
        function simulateExpiredTrial() {
            try {
                const expiredDate = new Date();
                expiredDate.setHours(expiredDate.getHours() - 25); // منتهية منذ ساعة
                
                const expiredTrialData = {
                    isActivated: true,
                    licenseType: 'trial',
                    activationDate: new Date(expiredDate.getTime() - (24 * 60 * 60 * 1000)).toISOString(),
                    expirationDate: expiredDate.toISOString(),
                    customerInfo: null,
                    trialStarted: true,
                    trialStartDate: new Date(expiredDate.getTime() - (24 * 60 * 60 * 1000)).toISOString(),
                    lastWarningDate: null,
                    usedLicenseKeys: []
                };
                
                localStorage.setItem('bagFactory_license', JSON.stringify(expiredTrialData));
                licenseSystem.licenseData = expiredTrialData;
                
                showResult('trialTestResult', 'تم إنشاء فترة تجريبية منتهية للاختبار', 'info');
                
            } catch (error) {
                showResult('trialTestResult', 'خطأ في محاكاة الفترة التجريبية المنتهية: ' + error.message, 'error');
            }
        }

        // اختبار الوصول بفترة تجريبية منتهية
        function testAccessWithExpiredTrial() {
            try {
                const access = licenseSystem.checkAccess();
                
                if (!access.allowed) {
                    showResult('trialTestResult', 
                        `✅ ممتاز! النظام يمنع الوصول بفترة تجريبية منتهية<br>
                        <span class="status-indicator status-blocked"></span>
                        الحالة: ${access.status}<br>
                        الرسالة: ${access.message}`, 'success');
                    testResults.expiredTrialBlocked = true;
                } else {
                    showResult('trialTestResult', 
                        `❌ خطأ! النظام يسمح بالوصول بفترة تجريبية منتهية<br>
                        <span class="status-indicator status-allowed"></span>
                        هذا يعني أن الوضع المجاني ما زال موجود!`, 'error');
                    testResults.expiredTrialBlocked = false;
                }
                
                updateTestSummary();
                
            } catch (error) {
                showResult('trialTestResult', 'خطأ في الاختبار: ' + error.message, 'error');
            }
        }

        // إعادة تعيين النظام
        function resetSystem() {
            try {
                localStorage.removeItem('bagFactory_license');
                localStorage.removeItem('bagFactory_usedKeys');
                licenseSystem.licenseData = licenseSystem.loadLicenseData();
                
                showResult('resetResult', 'تم إعادة تعيين النظام بنجاح', 'success');
                
                // فحص فوري للحالة
                setTimeout(() => {
                    checkCurrentStatus();
                }, 500);
                
            } catch (error) {
                showResult('resetResult', 'خطأ في إعادة التعيين: ' + error.message, 'error');
            }
        }

        // تحديث ملخص الاختبار
        function updateTestSummary() {
            const totalTests = 3;
            const passedTests = Object.values(testResults).filter(result => result === true).length;
            
            let summaryClass = 'info';
            let summaryIcon = '⚠️';
            
            if (passedTests === totalTests) {
                summaryClass = 'success';
                summaryIcon = '✅';
            } else if (passedTests === 0) {
                summaryClass = 'error';
                summaryIcon = '❌';
            }
            
            const summary = `
                ${summaryIcon} <strong>نتائج الاختبار:</strong><br>
                اجتاز ${passedTests} من ${totalTests} اختبارات<br><br>
                <strong>تفاصيل الاختبارات:</strong><br>
                ${testResults.noLicenseBlocked ? '✅' : '❌'} منع الوصول بدون ترخيص<br>
                ${testResults.expiredLicenseBlocked ? '✅' : '❌'} منع الوصول بترخيص منتهي<br>
                ${testResults.expiredTrialBlocked ? '✅' : '❌'} منع الوصول بفترة تجريبية منتهية<br><br>
                ${passedTests === totalTests ? 
                    '<strong class="text-success">🎉 النظام يعمل بشكل صحيح - تم إلغاء الوضع المجاني نهائياً!</strong>' : 
                    '<strong class="text-danger">⚠️ هناك مشاكل في النظام - الوضع المجاني قد يكون ما زال موجود</strong>'
                }
            `;
            
            showResult('testSummary', summary, summaryClass);
        }

        // عرض النتائج
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result-box ${type}">${message}</div>`;
        }

        // تحديث الصفحة عند التحميل
        window.onload = function() {
            checkCurrentStatus();
        };
    </script>
</body>
</html>
