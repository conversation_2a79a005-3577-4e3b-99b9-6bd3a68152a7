<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الترخيص</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="card neumorphic-card">
                    <div class="card-header">
                        <h3><i class="bi bi-bug me-2"></i>اختبار نظام الترخيص</h3>
                    </div>
                    <div class="card-body">
                        <!-- معلومات النظام -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>حالة النظام الحالية</h5>
                                <div id="systemStatus" class="alert alert-info">
                                    <div><strong>الحالة:</strong> <span id="currentStatus">-</span></div>
                                    <div><strong>نوع الترخيص:</strong> <span id="currentLicenseType">-</span></div>
                                    <div><strong>تاريخ الانتهاء:</strong> <span id="currentExpiration">-</span></div>
                                    <div><strong>الوقت المتبقي:</strong> <span id="currentTimeRemaining">-</span></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>أدوات الاختبار</h5>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="refreshStatus()">
                                        <i class="bi bi-arrow-clockwise me-2"></i>تحديث الحالة
                                    </button>
                                    <button class="btn btn-info" onclick="showLicenseModal()">
                                        <i class="bi bi-key me-2"></i>فتح نافذة الترخيص
                                    </button>
                                    <button class="btn btn-warning" onclick="testTrialExpiration()">
                                        <i class="bi bi-clock me-2"></i>محاكاة انتهاء الفترة التجريبية
                                    </button>
                                    <button class="btn btn-danger" onclick="clearAllData()">
                                        <i class="bi bi-trash me-2"></i>مسح جميع البيانات
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- زر مفتاح الترخيص -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>زر مفتاح الترخيص</h5>
                                <button type="button" id="licenseKeyBtn" class="btn btn-outline-warning neumorphic-btn" onclick="showLicenseModal()">
                                    <i class="bi bi-key-fill me-2"></i>
                                    <span id="licenseKeyText">مفتاح الترخيص</span>
                                </button>
                            </div>
                        </div>

                        <!-- بطاقة معلومات العميل -->
                        <div id="customerLicenseCard" class="row mb-4" style="display: none;">
                            <div class="col-12">
                                <div class="neumorphic-card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">
                                            <i class="bi bi-person-badge me-2"></i>
                                            معلومات المشترك
                                        </h5>
                                        <span id="licenseStatusBadge" class="badge bg-success">مشترك</span>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="info-item">
                                                    <i class="bi bi-person-fill text-primary me-2"></i>
                                                    <strong>الاسم:</strong>
                                                    <span id="dashboardCustomerName">-</span>
                                                </div>
                                                <div class="info-item">
                                                    <i class="bi bi-telephone-fill text-success me-2"></i>
                                                    <strong>الهاتف:</strong>
                                                    <span id="dashboardCustomerPhone">-</span>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="info-item">
                                                    <i class="bi bi-envelope-fill text-info me-2"></i>
                                                    <strong>البريد الإلكتروني:</strong>
                                                    <span id="dashboardCustomerEmail">-</span>
                                                </div>
                                                <div class="info-item">
                                                    <i class="bi bi-calendar-check text-warning me-2"></i>
                                                    <strong>تاريخ التفعيل:</strong>
                                                    <span id="dashboardActivationDate">-</span>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="info-item">
                                                    <i class="bi bi-calendar-x text-danger me-2"></i>
                                                    <strong>تاريخ الانتهاء:</strong>
                                                    <span id="dashboardExpirationDate">-</span>
                                                </div>
                                                <div class="info-item">
                                                    <i class="bi bi-hourglass-split text-primary me-2"></i>
                                                    <strong>المدة المتبقية:</strong>
                                                    <span id="dashboardRemainingDays" class="fw-bold text-success">-</span>
                                                </div>
                                                <div class="info-item">
                                                    <i class="bi bi-award text-secondary me-2"></i>
                                                    <strong>نوع الترخيص:</strong>
                                                    <span id="dashboardLicenseType">-</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- سجل الأحداث -->
                        <div class="row">
                            <div class="col-12">
                                <h5>سجل الأحداث</h5>
                                <div id="eventLog" class="alert alert-secondary" style="height: 200px; overflow-y: auto;">
                                    <div class="text-muted">سيتم عرض أحداث النظام هنا...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نوافذ الترخيص (نفس النوافذ من الملف الرئيسي) -->
    <!-- نافذة مفتاح الترخيص -->
    <div class="modal fade" id="licenseModal" tabindex="-1" aria-labelledby="licenseModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content neumorphic-card">
                <div class="modal-header">
                    <h5 class="modal-title" id="licenseModalLabel">
                        <i class="bi bi-key-fill me-2"></i>
                        إدارة الترخيص
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- حالة الترخيص -->
                    <div id="licenseStatusAlert" class="alert alert-warning">
                        <h6 id="licenseStatusTitle">لم يتم التفعيل</h6>
                        <p id="licenseStatusMessage" class="mb-0">لم يتم تفعيل أي ترخيص</p>
                    </div>

                    <!-- بطاقة معلومات العميل -->
                    <div id="customerInfoCard" class="card neumorphic-card mb-3" style="display: none;">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-person-fill me-2"></i>
                                معلومات المشترك
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>الاسم:</strong> <span id="customerName">-</span></p>
                                    <p><strong>الهاتف:</strong> <span id="customerPhone">-</span></p>
                                    <p><strong>البريد الإلكتروني:</strong> <span id="customerEmail">-</span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>تاريخ التفعيل:</strong> <span id="activationDate">-</span></p>
                                    <p><strong>تاريخ الانتهاء:</strong> <span id="expirationDate">-</span></p>
                                    <p><strong>المدة المتبقية:</strong> <span id="remainingDays">-</span></p>
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <button type="button" class="btn btn-danger" onclick="showCancelSubscriptionModal()">
                                    <i class="bi bi-x-circle me-2"></i>
                                    إلغاء الاشتراك
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقة إدخال الترخيص -->
                    <div id="licenseInputCard" class="card neumorphic-card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-key me-2"></i>
                                تفعيل الترخيص
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="licenseKey" class="form-label">مفتاح الترخيص</label>
                                <textarea class="form-control neumorphic-input" id="licenseKey" rows="4" 
                                         placeholder="الصق مفتاح الترخيص هنا..."></textarea>
                                <small class="text-muted">
                                    <i class="bi bi-info-circle me-1"></i>
                                    احصل على مفتاح الترخيص من المطور: 01100693019
                                </small>
                            </div>
                            <div class="text-center">
                                <button type="button" class="btn btn-success me-2" onclick="activateLicense()">
                                    <i class="bi bi-check-circle me-2"></i>
                                    تفعيل الترخيص
                                </button>
                                <button type="button" id="trialBtn" class="btn btn-info" onclick="startTrialPeriod()">
                                    <i class="bi bi-clock me-2"></i>
                                    فترة تجريبية (24 ساعة)
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد إلغاء الاشتراك -->
    <div class="modal fade" id="cancelSubscriptionModal" tabindex="-1" aria-labelledby="cancelSubscriptionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content neumorphic-card">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="cancelSubscriptionModalLabel">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        تأكيد إلغاء الاشتراك
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-warning me-2"></i>تحذير مهم</h6>
                        <p class="mb-0">
                            سيؤدي إلغاء الاشتراك إلى:
                        </p>
                        <ul class="mt-2 mb-0">
                            <li>فقدان جميع بيانات الترخيص</li>
                            <li>الخروج الفوري من البرنامج</li>
                            <li>عدم إمكانية التراجع عن هذا الإجراء</li>
                        </ul>
                    </div>
                    
                    <div class="mb-3">
                        <label for="cancelConfirmationInput" class="form-label">
                            للتأكيد، اكتب كلمة <strong>"إلغاء"</strong> في الحقل أدناه:
                        </label>
                        <input type="text" class="form-control" id="cancelConfirmationInput" 
                               placeholder="اكتب: إلغاء">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x me-2"></i>
                        إلغاء
                    </button>
                    <button type="button" id="confirmCancelBtn" class="btn btn-danger" 
                            onclick="confirmCancelSubscription()" disabled>
                        <i class="bi bi-trash me-2"></i>
                        تأكيد الإلغاء
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/license-system.js"></script>

    <script>
        // دوال الاختبار
        function refreshStatus() {
            if (typeof licenseSystem !== 'undefined') {
                const status = licenseSystem.checkLicenseStatus();

                document.getElementById('currentStatus').textContent = getStatusText(status.status);
                document.getElementById('currentLicenseType').textContent = licenseSystem.licenseData.licenseType || 'غير محدد';
                document.getElementById('currentExpiration').textContent = licenseSystem.licenseData.expirationDate ?
                    new Date(licenseSystem.licenseData.expirationDate).toLocaleDateString('ar-EG') : 'غير محدد';
                document.getElementById('currentTimeRemaining').textContent = status.message || 'غير محدد';

                // تحديث واجهة المستخدم
                licenseSystem.updateLicenseUI();
                licenseSystem.updateDashboardCustomerCard();

                logEvent('تم تحديث حالة النظام: ' + getStatusText(status.status));
            }
        }

        function getStatusText(status) {
            switch(status) {
                case 'active': return 'نشط';
                case 'trial': return 'فترة تجريبية';
                case 'warning': return 'تحذير انتهاء';
                case 'expired': return 'منتهي الصلاحية';
                case 'free': return 'مجاني';
                case 'inactive': return 'غير نشط';
                default: return 'غير معروف';
            }
        }

        function testTrialExpiration() {
            if (typeof licenseSystem !== 'undefined') {
                // محاكاة انتهاء الفترة التجريبية
                if (licenseSystem.licenseData.licenseType === 'trial') {
                    // تعديل تاريخ الانتهاء ليكون في الماضي
                    licenseSystem.licenseData.expirationDate = new Date(Date.now() - 1000).toISOString();
                    licenseSystem.saveLicenseData();

                    logEvent('تم محاكاة انتهاء الفترة التجريبية');

                    // إظهار التحذير
                    licenseSystem.showTrialExpiredWarning();

                    // تحديث الحالة
                    refreshStatus();
                } else {
                    alert('يجب أن تكون في فترة تجريبية لاختبار هذه الميزة');
                }
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات الترخيص؟')) {
                localStorage.removeItem('bagFactory_license');
                localStorage.removeItem('bagFactory_usedKeys');
                localStorage.removeItem('lastTrialWarning');

                logEvent('تم مسح جميع البيانات');

                // إعادة تحميل الصفحة
                location.reload();
            }
        }

        function logEvent(message) {
            const eventLog = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<small class="text-muted">[${timestamp}]</small> ${message}`;
            eventLog.appendChild(logEntry);
            eventLog.scrollTop = eventLog.scrollHeight;
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            logEvent('تم تحميل صفحة اختبار نظام الترخيص');

            // تحديث الحالة كل 5 ثوان
            setInterval(refreshStatus, 5000);

            // تحديث أولي
            setTimeout(refreshStatus, 1000);
        });
    </script>
