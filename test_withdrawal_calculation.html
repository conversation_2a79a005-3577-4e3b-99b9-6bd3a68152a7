<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حساب الكمية المسحوبة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #ffe8e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار حساب إجمالي الكمية المسحوبة</h1>
        
        <div class="test-section">
            <h3>إضافة بيانات تجريبية</h3>
            <button onclick="addTestData()">إضافة بيانات تجريبية</button>
            <button onclick="clearData()">مسح البيانات</button>
        </div>

        <div class="test-section">
            <h3>حساب الكمية</h3>
            <button onclick="calculateTotal()">حساب إجمالي الكمية</button>
            <div id="calculationResult"></div>
        </div>

        <div class="test-section">
            <h3>عرض البيانات المحفوظة</h3>
            <button onclick="showStoredData()">عرض البيانات</button>
            <div id="storedDataDisplay"></div>
        </div>

        <div class="test-section">
            <h3>اختبار دالة النظام</h3>
            <button onclick="testSystemFunction()">اختبار دالة refreshInventoryStats</button>
            <div id="systemTestResult"></div>
        </div>
    </div>

    <script>
        // إضافة بيانات تجريبية
        function addTestData() {
            const testData = [
                {
                    id: 'test1',
                    workerId: 'worker1',
                    workerName: 'أحمد محمد',
                    workerJob: 'عامل إنتاج',
                    productId: 'product1',
                    productName: 'حقيبة جلدية',
                    productCode: 'BAG001',
                    quantity: 10,
                    unitPrice: 50,
                    totalValue: 500,
                    date: new Date().toISOString().split('T')[0],
                    time: new Date().toLocaleTimeString('ar-EG'),
                    timestamp: new Date().toISOString(),
                    notes: 'اختبار 1'
                },
                {
                    id: 'test2',
                    workerId: 'worker2',
                    workerName: 'محمد علي',
                    workerJob: 'عامل تشطيب',
                    productId: 'product2',
                    productName: 'حقيبة قماش',
                    productCode: 'BAG002',
                    quantity: 15,
                    unitPrice: 30,
                    totalValue: 450,
                    date: new Date().toISOString().split('T')[0],
                    time: new Date().toLocaleTimeString('ar-EG'),
                    timestamp: new Date().toISOString(),
                    notes: 'اختبار 2'
                },
                {
                    id: 'test3',
                    workerId: 'worker3',
                    workerName: 'علي أحمد',
                    workerJob: 'عامل تعبئة',
                    productId: 'product3',
                    productName: 'حقيبة رياضية',
                    productCode: 'BAG003',
                    quantity: 8,
                    unitPrice: 75,
                    totalValue: 600,
                    date: new Date().toISOString().split('T')[0],
                    time: new Date().toLocaleTimeString('ar-EG'),
                    timestamp: new Date().toISOString(),
                    notes: 'اختبار 3'
                }
            ];

            localStorage.setItem('bagFactory_workerWithdrawals', JSON.stringify(testData));
            document.getElementById('calculationResult').innerHTML = '<div class="result">تم إضافة البيانات التجريبية بنجاح!</div>';
        }

        // مسح البيانات
        function clearData() {
            localStorage.removeItem('bagFactory_workerWithdrawals');
            document.getElementById('calculationResult').innerHTML = '<div class="result">تم مسح البيانات!</div>';
            document.getElementById('storedDataDisplay').innerHTML = '';
        }

        // حساب إجمالي الكمية
        function calculateTotal() {
            const saved = localStorage.getItem('bagFactory_workerWithdrawals');
            const withdrawals = saved ? JSON.parse(saved) : [];
            
            let totalQuantity = 0;
            let details = '<h4>تفاصيل الحساب:</h4>';
            
            if (withdrawals.length === 0) {
                document.getElementById('calculationResult').innerHTML = '<div class="error">لا توجد بيانات للحساب</div>';
                return;
            }

            details += '<table><tr><th>العامل</th><th>المنتج</th><th>الكمية</th></tr>';
            
            withdrawals.forEach((withdrawal, index) => {
                if (withdrawal && withdrawal.quantity !== undefined && withdrawal.quantity !== null) {
                    const quantity = Number(withdrawal.quantity);
                    if (!isNaN(quantity) && quantity > 0) {
                        totalQuantity += quantity;
                        details += `<tr><td>${withdrawal.workerName}</td><td>${withdrawal.productName}</td><td>${quantity}</td></tr>`;
                    }
                }
            });
            
            details += '</table>';
            details += `<div class="result"><strong>إجمالي الكمية المسحوبة: ${totalQuantity.toLocaleString()}</strong></div>`;
            
            document.getElementById('calculationResult').innerHTML = details;
        }

        // عرض البيانات المحفوظة
        function showStoredData() {
            const saved = localStorage.getItem('bagFactory_workerWithdrawals');
            const withdrawals = saved ? JSON.parse(saved) : [];
            
            let display = '<h4>البيانات المحفوظة:</h4>';
            display += `<p>عدد السجلات: ${withdrawals.length}</p>`;
            
            if (withdrawals.length > 0) {
                display += '<pre>' + JSON.stringify(withdrawals, null, 2) + '</pre>';
            } else {
                display += '<div class="error">لا توجد بيانات محفوظة</div>';
            }
            
            document.getElementById('storedDataDisplay').innerHTML = display;
        }

        // اختبار دالة النظام
        function testSystemFunction() {
            // محاكاة دالة refreshInventoryStats
            const saved = localStorage.getItem('bagFactory_workerWithdrawals');
            const workerWithdrawals = saved ? JSON.parse(saved) : [];
            
            console.log('عدد سجلات السحب المحملة:', workerWithdrawals.length);
            console.log('بيانات السحب:', workerWithdrawals);

            let totalWithdrawnQuantity = 0;
            
            if (workerWithdrawals && Array.isArray(workerWithdrawals)) {
                workerWithdrawals.forEach((withdrawal, index) => {
                    if (withdrawal && withdrawal.quantity !== undefined && withdrawal.quantity !== null) {
                        const quantity = Number(withdrawal.quantity);
                        if (!isNaN(quantity) && quantity > 0) {
                            totalWithdrawnQuantity += quantity;
                            console.log(`السجل ${index + 1}: العامل ${withdrawal.workerName}, المنتج ${withdrawal.productName}, الكمية ${quantity}`);
                        }
                    }
                });
            }

            console.log('إجمالي الكمية المسحوبة المحسوبة:', totalWithdrawnQuantity);

            let result = '<div class="result">';
            result += `<strong>نتيجة اختبار دالة النظام:</strong><br>`;
            result += `عدد السجلات: ${workerWithdrawals.length}<br>`;
            result += `إجمالي الكمية المسحوبة: ${totalWithdrawnQuantity.toLocaleString()}<br>`;
            result += `تحقق من وحدة التحكم (Console) لمزيد من التفاصيل`;
            result += '</div>';
            
            document.getElementById('systemTestResult').innerHTML = result;
        }

        // تحديث تلقائي عند تحميل الصفحة
        window.onload = function() {
            showStoredData();
        };
    </script>
</body>
</html>
