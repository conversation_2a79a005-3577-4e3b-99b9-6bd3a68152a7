<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الأساسي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
        }
        .result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #ffe8e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #todayProduction {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار النظام الأساسي - حساب الكمية المسحوبة</h1>
        
        <div class="test-section">
            <h3>محاكاة عنصر الإحصائيات</h3>
            <div class="stat-card">
                <div class="stat-info">
                    <h3 id="todayProduction">0</h3>
                    <p>إجمالي الكمية المسحوبة</p>
                    <button onclick="refreshInventoryStats()" title="تحديث الإحصائيات">
                        <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>إدارة البيانات التجريبية</h3>
            <button onclick="addTestData()">إضافة بيانات تجريبية</button>
            <button onclick="clearData()">مسح البيانات</button>
            <button onclick="showData()">عرض البيانات</button>
        </div>

        <div class="test-section">
            <h3>أدوات التشخيص</h3>
            <button onclick="diagnoseWithdrawals()">تشخيص البيانات</button>
            <button onclick="testCalculation()">اختبار الحساب</button>
            <div id="diagnosticResult"></div>
        </div>

        <div class="test-section">
            <h3>عرض البيانات</h3>
            <div id="dataDisplay"></div>
        </div>
    </div>

    <!-- تضمين ملفات النظام الأساسي -->
    <script src="js/app.js"></script>
    
    <script>
        // متغير النظام
        let app;

        // تهيئة النظام
        window.onload = function() {
            try {
                app = new BagFactorySystem();
                console.log('تم تهيئة النظام بنجاح');
                showData();
            } catch (error) {
                console.error('خطأ في تهيئة النظام:', error);
                document.getElementById('diagnosticResult').innerHTML = '<div class="error">خطأ في تهيئة النظام: ' + error.message + '</div>';
            }
        };

        // إضافة بيانات تجريبية
        function addTestData() {
            const testData = [
                {
                    id: 'test1',
                    workerId: 'worker1',
                    workerName: 'أحمد محمد',
                    workerJob: 'عامل إنتاج',
                    productId: 'product1',
                    productName: 'حقيبة جلدية',
                    productCode: 'BAG001',
                    quantity: 25,
                    unitPrice: 50,
                    totalValue: 1250,
                    date: new Date().toISOString().split('T')[0],
                    time: new Date().toLocaleTimeString('ar-EG'),
                    timestamp: new Date().toISOString(),
                    notes: 'اختبار 1'
                },
                {
                    id: 'test2',
                    workerId: 'worker2',
                    workerName: 'محمد علي',
                    workerJob: 'عامل تشطيب',
                    productId: 'product2',
                    productName: 'حقيبة قماش',
                    productCode: 'BAG002',
                    quantity: 30,
                    unitPrice: 30,
                    totalValue: 900,
                    date: new Date().toISOString().split('T')[0],
                    time: new Date().toLocaleTimeString('ar-EG'),
                    timestamp: new Date().toISOString(),
                    notes: 'اختبار 2'
                },
                {
                    id: 'test3',
                    workerId: 'worker3',
                    workerName: 'علي أحمد',
                    workerJob: 'عامل تعبئة',
                    productId: 'product3',
                    productName: 'حقيبة رياضية',
                    productCode: 'BAG003',
                    quantity: 20,
                    unitPrice: 75,
                    totalValue: 1500,
                    date: new Date().toISOString().split('T')[0],
                    time: new Date().toLocaleTimeString('ar-EG'),
                    timestamp: new Date().toISOString(),
                    notes: 'اختبار 3'
                }
            ];

            localStorage.setItem('bagFactory_workerWithdrawals', JSON.stringify(testData));
            
            // تحديث بيانات النظام
            if (app) {
                app.workerWithdrawals = testData;
            }
            
            showData();
            document.getElementById('diagnosticResult').innerHTML = '<div class="result">تم إضافة البيانات التجريبية بنجاح! (المجموع المتوقع: 75)</div>';
        }

        // مسح البيانات
        function clearData() {
            localStorage.removeItem('bagFactory_workerWithdrawals');
            if (app) {
                app.workerWithdrawals = [];
            }
            document.getElementById('todayProduction').textContent = '0';
            showData();
            document.getElementById('diagnosticResult').innerHTML = '<div class="result">تم مسح البيانات!</div>';
        }

        // عرض البيانات
        function showData() {
            const saved = localStorage.getItem('bagFactory_workerWithdrawals');
            const withdrawals = saved ? JSON.parse(saved) : [];
            
            let display = `<h4>البيانات المحفوظة (${withdrawals.length} سجل):</h4>`;
            
            if (withdrawals.length > 0) {
                let totalQuantity = 0;
                display += '<table border="1" style="width: 100%; border-collapse: collapse;">';
                display += '<tr><th>العامل</th><th>المنتج</th><th>الكمية</th><th>القيمة</th></tr>';
                
                withdrawals.forEach(w => {
                    const quantity = Number(w.quantity);
                    if (!isNaN(quantity) && quantity > 0) {
                        totalQuantity += quantity;
                    }
                    display += `<tr>
                        <td>${w.workerName}</td>
                        <td>${w.productName}</td>
                        <td>${w.quantity}</td>
                        <td>${w.totalValue} ج.م</td>
                    </tr>`;
                });
                
                display += '</table>';
                display += `<p><strong>المجموع المحسوب: ${totalQuantity}</strong></p>`;
            } else {
                display += '<div class="error">لا توجد بيانات</div>';
            }
            
            document.getElementById('dataDisplay').innerHTML = display;
        }

        // اختبار الحساب
        function testCalculation() {
            const saved = localStorage.getItem('bagFactory_workerWithdrawals');
            const withdrawals = saved ? JSON.parse(saved) : [];
            
            let totalQuantity = 0;
            let details = '<h4>تفاصيل الحساب:</h4>';
            
            if (withdrawals.length === 0) {
                document.getElementById('diagnosticResult').innerHTML = '<div class="error">لا توجد بيانات للحساب</div>';
                return;
            }

            withdrawals.forEach((withdrawal, index) => {
                if (withdrawal && withdrawal.quantity !== undefined && withdrawal.quantity !== null) {
                    const quantity = Number(withdrawal.quantity);
                    if (!isNaN(quantity) && quantity > 0) {
                        totalQuantity += quantity;
                        details += `<p>السجل ${index + 1}: ${withdrawal.workerName} - ${withdrawal.productName} - الكمية: ${quantity}</p>`;
                    }
                }
            });
            
            details += `<div class="result"><strong>إجمالي الكمية المحسوبة: ${totalQuantity}</strong></div>`;
            
            document.getElementById('diagnosticResult').innerHTML = details;
        }
    </script>
</body>
</html>
