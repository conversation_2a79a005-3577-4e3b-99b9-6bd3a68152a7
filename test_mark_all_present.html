<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر تسجيل حضور الكل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">اختبار زر تسجيل حضور الكل</h1>
        
        <div class="mb-3">
            <label for="attendanceDate" class="form-label">تاريخ الحضور:</label>
            <input type="date" class="form-control" id="attendanceDate">
        </div>
        
        <div class="text-center mb-4">
            <button class="btn btn-success btn-lg" onclick="testMarkAllPresent()">
                <i class="bi bi-check-all me-2"></i>اختبار تسجيل حضور الكل
            </button>
        </div>
        
        <div id="testResults"></div>
        
        <div class="mt-4">
            <h3>معلومات النظام:</h3>
            <div id="systemInfo"></div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        function testMarkAllPresent() {
            addTestResult('بدء اختبار وظيفة تسجيل حضور الكل...', 'info');
            
            try {
                // التحقق من وجود التطبيق
                if (typeof app === 'undefined') {
                    addTestResult('خطأ: متغير app غير معرف', 'error');
                    return;
                }
                
                if (!app) {
                    addTestResult('خطأ: التطبيق غير مهيأ', 'error');
                    return;
                }
                
                addTestResult('✓ التطبيق مهيأ بنجاح', 'success');
                
                // التحقق من وجود العمال
                if (!app.workers) {
                    addTestResult('خطأ: قائمة العمال غير موجودة', 'error');
                    return;
                }
                
                addTestResult(`✓ عدد العمال: ${app.workers.length}`, 'success');
                
                // التحقق من وجود عمال نشطين
                const activeWorkers = app.workers.filter(w => w.status === 'active');
                addTestResult(`✓ عدد العمال النشطين: ${activeWorkers.length}`, 'success');
                
                // التحقق من وجود بيانات الحضور
                if (!app.attendance) {
                    addTestResult('خطأ: بيانات الحضور غير موجودة', 'error');
                    return;
                }
                
                addTestResult('✓ بيانات الحضور موجودة', 'success');
                
                // التحقق من وجود الوظيفة
                if (typeof app.markAllPresent !== 'function') {
                    addTestResult('خطأ: وظيفة markAllPresent غير موجودة', 'error');
                    return;
                }
                
                addTestResult('✓ وظيفة markAllPresent موجودة', 'success');
                
                // تشغيل الوظيفة
                addTestResult('تشغيل وظيفة markAllPresent...', 'info');
                app.markAllPresent();
                addTestResult('✓ تم تشغيل الوظيفة بنجاح', 'success');
                
                // التحقق من النتائج
                const date = document.getElementById('attendanceDate').value || new Date().toISOString().split('T')[0];
                if (app.attendance[date]) {
                    const presentToday = Object.keys(app.attendance[date]).length;
                    addTestResult(`✓ تم تسجيل حضور ${presentToday} عامل لتاريخ ${date}`, 'success');
                } else {
                    addTestResult('تحذير: لم يتم العثور على بيانات حضور لهذا التاريخ', 'error');
                }
                
            } catch (error) {
                addTestResult(`خطأ في التشغيل: ${error.message}`, 'error');
                console.error('Error:', error);
            }
        }

        function updateSystemInfo() {
            const infoDiv = document.getElementById('systemInfo');
            let info = '<div class="row">';
            
            if (typeof app !== 'undefined' && app) {
                info += `
                    <div class="col-md-6">
                        <strong>العمال:</strong> ${app.workers ? app.workers.length : 0}<br>
                        <strong>العمال النشطين:</strong> ${app.workers ? app.workers.filter(w => w.status === 'active').length : 0}<br>
                        <strong>المنتجات:</strong> ${app.products ? app.products.length : 0}<br>
                    </div>
                    <div class="col-md-6">
                        <strong>المستخدمين:</strong> ${app.users ? app.users.length : 0}<br>
                        <strong>بيانات الحضور:</strong> ${app.attendance ? Object.keys(app.attendance).length : 0} يوم<br>
                        <strong>حالة التطبيق:</strong> <span class="text-success">مهيأ</span><br>
                    </div>
                `;
            } else {
                info += '<div class="col-12"><span class="text-danger">التطبيق غير مهيأ</span></div>';
            }
            
            info += '</div>';
            infoDiv.innerHTML = info;
        }

        // تحديث معلومات النظام كل ثانية
        setInterval(updateSystemInfo, 1000);
        
        // تعيين التاريخ الحالي
        document.getElementById('attendanceDate').value = new Date().toISOString().split('T')[0];
    </script>
</body>
</html>
