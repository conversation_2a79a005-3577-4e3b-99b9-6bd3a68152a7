<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر إجمالي الكمية المسحوبة الجديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
            text-align: center;
        }
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            color: white;
        }
        .bg-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        #todayProduction {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin: 10px 0;
        }
        .btn {
            border-radius: 10px;
            padding: 8px 16px;
            font-weight: 500;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">
            <i class="bi bi-gear-fill me-2"></i>
            اختبار زر إجمالي الكمية المسحوبة الجديد
        </h1>

        <!-- محاكاة بطاقة الإحصائيات -->
        <div class="stat-card">
            <div class="stat-icon bg-warning">
                <i class="bi bi-box-arrow-down"></i>
            </div>
            <div class="stat-info">
                <h3 id="todayProduction">0</h3>
                <p>إجمالي الكمية المسحوبة</p>
                <button class="btn btn-sm btn-outline-primary mt-2" 
                        onclick="refreshWithdrawnQuantity()" 
                        title="تحديث إجمالي الكمية المسحوبة تلقائياً">
                    <i class="bi bi-arrow-clockwise me-1"></i>تحديث تلقائي
                </button>
            </div>
        </div>

        <!-- قسم الاختبار -->
        <div class="test-section">
            <h3><i class="bi bi-clipboard-check me-2"></i>اختبار الوظائف</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-success w-100 mb-2" onclick="loadSampleData()">
                        <i class="bi bi-database-add me-2"></i>تحميل بيانات تجريبية
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-danger w-100 mb-2" onclick="clearData()">
                        <i class="bi bi-trash me-2"></i>مسح البيانات
                    </button>
                </div>
            </div>

            <button class="btn btn-info w-100 mb-2" onclick="showStoredData()">
                <i class="bi bi-eye me-2"></i>عرض البيانات المحفوظة
            </button>

            <button class="btn btn-warning w-100" onclick="testAutoUpdate()">
                <i class="bi bi-arrow-repeat me-2"></i>اختبار التحديث التلقائي
            </button>
        </div>

        <!-- منطقة النتائج -->
        <div id="results" class="test-section">
            <h4><i class="bi bi-graph-up me-2"></i>النتائج:</h4>
            <div id="resultContent">
                <p class="text-muted">لا توجد نتائج بعد...</p>
            </div>
        </div>

        <!-- عرض البيانات -->
        <div id="dataDisplay" class="test-section" style="display: none;">
            <h4><i class="bi bi-table me-2"></i>البيانات المحفوظة:</h4>
            <div id="dataContent"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // محاكاة كائن app
        const app = {
            autoUpdateWithdrawnQuantity: function() {
                const saved = localStorage.getItem('bagFactory_workerWithdrawals');
                const workerWithdrawals = saved ? JSON.parse(saved) : [];
                
                let totalWithdrawnQuantity = 0;
                
                if (workerWithdrawals && Array.isArray(workerWithdrawals)) {
                    totalWithdrawnQuantity = workerWithdrawals.reduce((sum, withdrawal) => {
                        if (withdrawal && withdrawal.quantity !== undefined && withdrawal.quantity !== null) {
                            const quantity = Number(withdrawal.quantity);
                            if (!isNaN(quantity) && quantity > 0) {
                                return sum + quantity;
                            }
                        }
                        return sum;
                    }, 0);
                }
                
                // تحديث العنصر في الواجهة
                const todayProductionElement = document.getElementById('todayProduction');
                if (todayProductionElement) {
                    todayProductionElement.textContent = totalWithdrawnQuantity.toLocaleString();
                }
                
                return totalWithdrawnQuantity;
            },
            
            showSuccess: function(message) {
                const alertDiv = document.createElement('div');
                alertDiv.className = 'success';
                alertDiv.innerHTML = `<i class="bi bi-check-circle-fill me-2"></i>${message}`;
                document.getElementById('resultContent').appendChild(alertDiv);
                
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 3000);
            }
        };

        // الدالة الجديدة لتحديث إجمالي الكمية المسحوبة
        function refreshWithdrawnQuantity() {
            if (app) {
                console.log('تحديث إجمالي الكمية المسحوبة تلقائياً...');
                
                // تحديث الكمية المسحوبة تلقائياً
                const totalWithdrawn = app.autoUpdateWithdrawnQuantity();
                
                // إظهار رسالة تأكيد للمستخدم
                const todayProductionElement = document.getElementById('todayProduction');
                if (todayProductionElement) {
                    // إضافة تأثير بصري للتحديث
                    todayProductionElement.style.transition = 'all 0.3s ease';
                    todayProductionElement.style.transform = 'scale(1.1)';
                    todayProductionElement.style.color = '#28a745';
                    
                    setTimeout(() => {
                        todayProductionElement.style.transform = 'scale(1)';
                        todayProductionElement.style.color = '';
                    }, 300);
                }
                
                // إظهار رسالة نجح التحديث
                app.showSuccess(`تم تحديث إجمالي الكمية المسحوبة تلقائياً: ${totalWithdrawn.toLocaleString()} قطعة`);
                
                console.log('تم تحديث إجمالي الكمية المسحوبة:', totalWithdrawn);
            }
        }

        // تحميل بيانات تجريبية
        function loadSampleData() {
            const sampleData = [
                {
                    id: '1',
                    workerId: 'w1',
                    workerName: 'أحمد محمد',
                    workerJob: 'عامل إنتاج',
                    productId: 'p1',
                    productName: 'شنطة يد',
                    quantity: 25,
                    unitPrice: 50,
                    totalValue: 1250,
                    date: '2024-01-15',
                    time: '09:30:00'
                },
                {
                    id: '2',
                    workerId: 'w2',
                    workerName: 'فاطمة علي',
                    workerJob: 'عاملة تشطيب',
                    productId: 'p2',
                    productName: 'شنطة ظهر',
                    quantity: 30,
                    unitPrice: 75,
                    totalValue: 2250,
                    date: '2024-01-16',
                    time: '10:15:00'
                },
                {
                    id: '3',
                    workerId: 'w3',
                    workerName: 'محمد حسن',
                    workerJob: 'فني',
                    productId: 'p3',
                    productName: 'شنطة رياضية',
                    quantity: 20,
                    unitPrice: 60,
                    totalValue: 1200,
                    date: '2024-01-17',
                    time: '11:00:00'
                }
            ];
            
            localStorage.setItem('bagFactory_workerWithdrawals', JSON.stringify(sampleData));
            app.autoUpdateWithdrawnQuantity();
            showMessage('تم تحميل البيانات التجريبية بنجاح!', 'success');
        }

        // مسح البيانات
        function clearData() {
            localStorage.removeItem('bagFactory_workerWithdrawals');
            app.autoUpdateWithdrawnQuantity();
            showMessage('تم مسح جميع البيانات!', 'error');
        }

        // عرض البيانات المحفوظة
        function showStoredData() {
            const saved = localStorage.getItem('bagFactory_workerWithdrawals');
            const data = saved ? JSON.parse(saved) : [];
            
            const dataDisplay = document.getElementById('dataDisplay');
            const dataContent = document.getElementById('dataContent');
            
            if (data.length === 0) {
                dataContent.innerHTML = '<p class="text-muted">لا توجد بيانات محفوظة</p>';
            } else {
                let html = `<p><strong>عدد السجلات:</strong> ${data.length}</p>`;
                html += '<table class="table table-striped table-sm">';
                html += '<thead><tr><th>العامل</th><th>المنتج</th><th>الكمية</th><th>التاريخ</th></tr></thead>';
                html += '<tbody>';
                
                data.forEach(item => {
                    html += `<tr>
                        <td>${item.workerName}</td>
                        <td>${item.productName}</td>
                        <td>${item.quantity}</td>
                        <td>${item.date}</td>
                    </tr>`;
                });
                
                html += '</tbody></table>';
                dataContent.innerHTML = html;
            }
            
            dataDisplay.style.display = 'block';
        }

        // اختبار التحديث التلقائي
        function testAutoUpdate() {
            refreshWithdrawnQuantity();
        }

        // عرض رسالة
        function showMessage(message, type) {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `<i class="bi bi-info-circle-fill me-2"></i>${message}`;
            document.getElementById('resultContent').appendChild(div);
            
            setTimeout(() => {
                if (div.parentNode) {
                    div.remove();
                }
            }, 3000);
        }

        // تحديث الإحصائيات عند تحميل الصفحة
        window.addEventListener('load', function() {
            app.autoUpdateWithdrawnQuantity();
        });
    </script>
</body>
</html>
